/*!
 * MediaElement.js
 * http://www.mediaelementjs.com/
 *
 * Wrapper that mimics native HTML5 MediaElement (audio and video)
 * using a variety of technologies (pure JavaScript, Flash, iframe)
 *
 * Copyright 2010-2017, <PERSON> (http://j.hn/)
 * License: MIT
 *
 */
!function i(o,l,s){function d(n,e){if(!l[n]){if(!o[n]){var t="function"==typeof require&&require;if(!e&&t)return t(n,!0);if(u)return u(n,!0);var r=new Error("Cannot find module '"+n+"'");throw r.code="MODULE_NOT_FOUND",r}var a=l[n]={exports:{}};o[n][0].call(a.exports,function(e){var t=o[n][1][e];return d(t||e)},a,a.exports,i,o,l,s)}return l[n].exports}for(var u="function"==typeof require&&require,e=0;e<s.length;e++)d(s[e]);return d}({1:[function(e,t,n){},{}],2:[function(a,i,e){(function(e){var t,n=void 0!==e?e:"undefined"!=typeof window?window:{},r=a(1);"undefined"!=typeof document?t=document:(t=n["__GLOBAL_DOCUMENT_CACHE@4"])||(t=n["__GLOBAL_DOCUMENT_CACHE@4"]=r),i.exports=t}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{1:1}],3:[function(e,n,t){(function(e){var t;t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:{},n.exports=t}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],4:[function(e,n,t){!function(e){var t=setTimeout;function r(){}function i(e){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof e)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=void 0,this._deferreds=[],d(e,this)}function a(n,r){for(;3===n._state;)n=n._value;0!==n._state?(n._handled=!0,i._immediateFn(function(){var e=1===n._state?r.onFulfilled:r.onRejected;if(null!==e){var t;try{t=e(n._value)}catch(e){return void l(r.promise,e)}o(r.promise,t)}else(1===n._state?o:l)(r.promise,n._value)})):n._deferreds.push(r)}function o(t,e){try{if(e===t)throw new TypeError("A promise cannot be resolved with itself.");if(e&&("object"==typeof e||"function"==typeof e)){var n=e.then;if(e instanceof i)return t._state=3,t._value=e,void s(t);if("function"==typeof n)return void d((r=n,a=e,function(){r.apply(a,arguments)}),t)}t._state=1,t._value=e,s(t)}catch(e){l(t,e)}var r,a}function l(e,t){e._state=2,e._value=t,s(e)}function s(e){2===e._state&&0===e._deferreds.length&&i._immediateFn(function(){e._handled||i._unhandledRejectionFn(e._value)});for(var t=0,n=e._deferreds.length;t<n;t++)a(e,e._deferreds[t]);e._deferreds=null}function d(e,t){var n=!1;try{e(function(e){n||(n=!0,o(t,e))},function(e){n||(n=!0,l(t,e))})}catch(e){if(n)return;n=!0,l(t,e)}}i.prototype.catch=function(e){return this.then(null,e)},i.prototype.then=function(e,t){var n=new this.constructor(r);return a(this,new function(e,t,n){this.onFulfilled="function"==typeof e?e:null,this.onRejected="function"==typeof t?t:null,this.promise=n}(e,t,n)),n},i.all=function(e){var l=Array.prototype.slice.call(e);return new i(function(r,a){if(0===l.length)return r([]);var i=l.length;function o(t,e){try{if(e&&("object"==typeof e||"function"==typeof e)){var n=e.then;if("function"==typeof n)return void n.call(e,function(e){o(t,e)},a)}l[t]=e,0==--i&&r(l)}catch(e){a(e)}}for(var e=0;e<l.length;e++)o(e,l[e])})},i.resolve=function(t){return t&&"object"==typeof t&&t.constructor===i?t:new i(function(e){e(t)})},i.reject=function(n){return new i(function(e,t){t(n)})},i.race=function(a){return new i(function(e,t){for(var n=0,r=a.length;n<r;n++)a[n].then(e,t)})},i._immediateFn="function"==typeof setImmediate&&function(e){setImmediate(e)}||function(e){t(e,0)},i._unhandledRejectionFn=function(e){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",e)},i._setImmediateFn=function(e){i._immediateFn=e},i._setUnhandledRejectionFn=function(e){i._unhandledRejectionFn=e},void 0!==n&&n.exports?n.exports=i:e.Promise||(e.Promise=i)}(this)},{}],5:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r,o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a=e(7),i=(r=a)&&r.__esModule?r:{default:r},l=e(9),s=e(18);var d={lang:"en",en:l.EN,language:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];if(null!=t&&t.length){if("string"!=typeof t[0])throw new TypeError("Language code must be a string value");if(!/^[a-z]{2,3}((\-|_)[a-z]{2})?$/i.test(t[0]))throw new TypeError("Language code must have format 2-3 letters and. optionally, hyphen, underscore followed by 2 more letters");d.lang=t[0],void 0===d[t[0]]?(t[1]=null!==t[1]&&void 0!==t[1]&&"object"===o(t[1])?t[1]:{},d[t[0]]=(0,s.isObjectEmpty)(t[1])?l.EN:t[1]):null!==t[1]&&void 0!==t[1]&&"object"===o(t[1])&&(d[t[0]]=t[1])}return d.lang},t:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null;if("string"==typeof e&&e.length){var n=void 0,r=void 0,a=d.language(),i=function(e,t,n){return"object"!==(void 0===e?"undefined":o(e))||"number"!=typeof t||"number"!=typeof n?e:[function(){return arguments.length<=1?void 0:arguments[1]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:arguments.length<=2?void 0:arguments[2]},function(){return 0===(arguments.length<=0?void 0:arguments[0])||1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:arguments.length<=2?void 0:arguments[2]},function(){return(arguments.length<=0?void 0:arguments[0])%10==1&&(arguments.length<=0?void 0:arguments[0])%100!=11?arguments.length<=1?void 0:arguments[1]:0!==(arguments.length<=0?void 0:arguments[0])?arguments.length<=2?void 0:arguments[2]:arguments.length<=3?void 0:arguments[3]},function(){return 1===(arguments.length<=0?void 0:arguments[0])||11===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:2===(arguments.length<=0?void 0:arguments[0])||12===(arguments.length<=0?void 0:arguments[0])?arguments.length<=2?void 0:arguments[2]:2<(arguments.length<=0?void 0:arguments[0])&&(arguments.length<=0?void 0:arguments[0])<20?arguments.length<=3?void 0:arguments[3]:arguments.length<=4?void 0:arguments[4]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:0===(arguments.length<=0?void 0:arguments[0])||0<(arguments.length<=0?void 0:arguments[0])%100&&(arguments.length<=0?void 0:arguments[0])%100<20?arguments.length<=2?void 0:arguments[2]:arguments.length<=3?void 0:arguments[3]},function(){return(arguments.length<=0?void 0:arguments[0])%10==1&&(arguments.length<=0?void 0:arguments[0])%100!=11?arguments.length<=1?void 0:arguments[1]:2<=(arguments.length<=0?void 0:arguments[0])%10&&((arguments.length<=0?void 0:arguments[0])%100<10||20<=(arguments.length<=0?void 0:arguments[0])%100)?arguments.length<=2?void 0:arguments[2]:[3]},function(){return(arguments.length<=0?void 0:arguments[0])%10==1&&(arguments.length<=0?void 0:arguments[0])%100!=11?arguments.length<=1?void 0:arguments[1]:2<=(arguments.length<=0?void 0:arguments[0])%10&&(arguments.length<=0?void 0:arguments[0])%10<=4&&((arguments.length<=0?void 0:arguments[0])%100<10||20<=(arguments.length<=0?void 0:arguments[0])%100)?arguments.length<=2?void 0:arguments[2]:arguments.length<=3?void 0:arguments[3]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:2<=(arguments.length<=0?void 0:arguments[0])&&(arguments.length<=0?void 0:arguments[0])<=4?arguments.length<=2?void 0:arguments[2]:arguments.length<=3?void 0:arguments[3]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:2<=(arguments.length<=0?void 0:arguments[0])%10&&(arguments.length<=0?void 0:arguments[0])%10<=4&&((arguments.length<=0?void 0:arguments[0])%100<10||20<=(arguments.length<=0?void 0:arguments[0])%100)?arguments.length<=2?void 0:arguments[2]:arguments.length<=3?void 0:arguments[3]},function(){return(arguments.length<=0?void 0:arguments[0])%100==1?arguments.length<=2?void 0:arguments[2]:(arguments.length<=0?void 0:arguments[0])%100==2?arguments.length<=3?void 0:arguments[3]:(arguments.length<=0?void 0:arguments[0])%100==3||(arguments.length<=0?void 0:arguments[0])%100==4?arguments.length<=4?void 0:arguments[4]:arguments.length<=1?void 0:arguments[1]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:2===(arguments.length<=0?void 0:arguments[0])?arguments.length<=2?void 0:arguments[2]:2<(arguments.length<=0?void 0:arguments[0])&&(arguments.length<=0?void 0:arguments[0])<7?arguments.length<=3?void 0:arguments[3]:6<(arguments.length<=0?void 0:arguments[0])&&(arguments.length<=0?void 0:arguments[0])<11?arguments.length<=4?void 0:arguments[4]:arguments.length<=5?void 0:arguments[5]},function(){return 0===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=2?void 0:arguments[2]:2===(arguments.length<=0?void 0:arguments[0])?arguments.length<=3?void 0:arguments[3]:3<=(arguments.length<=0?void 0:arguments[0])%100&&(arguments.length<=0?void 0:arguments[0])%100<=10?arguments.length<=4?void 0:arguments[4]:11<=(arguments.length<=0?void 0:arguments[0])%100?arguments.length<=5?void 0:arguments[5]:arguments.length<=6?void 0:arguments[6]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:0===(arguments.length<=0?void 0:arguments[0])||1<(arguments.length<=0?void 0:arguments[0])%100&&(arguments.length<=0?void 0:arguments[0])%100<11?arguments.length<=2?void 0:arguments[2]:10<(arguments.length<=0?void 0:arguments[0])%100&&(arguments.length<=0?void 0:arguments[0])%100<20?arguments.length<=3?void 0:arguments[3]:arguments.length<=4?void 0:arguments[4]},function(){return(arguments.length<=0?void 0:arguments[0])%10==1?arguments.length<=1?void 0:arguments[1]:(arguments.length<=0?void 0:arguments[0])%10==2?arguments.length<=2?void 0:arguments[2]:arguments.length<=3?void 0:arguments[3]},function(){return 11!==(arguments.length<=0?void 0:arguments[0])&&(arguments.length<=0?void 0:arguments[0])%10==1?arguments.length<=1?void 0:arguments[1]:arguments.length<=2?void 0:arguments[2]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:2<=(arguments.length<=0?void 0:arguments[0])%10&&(arguments.length<=0?void 0:arguments[0])%10<=4&&((arguments.length<=0?void 0:arguments[0])%100<10||20<=(arguments.length<=0?void 0:arguments[0])%100)?arguments.length<=2?void 0:arguments[2]:arguments.length<=3?void 0:arguments[3]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:2===(arguments.length<=0?void 0:arguments[0])?arguments.length<=2?void 0:arguments[2]:8!==(arguments.length<=0?void 0:arguments[0])&&11!==(arguments.length<=0?void 0:arguments[0])?arguments.length<=3?void 0:arguments[3]:arguments.length<=4?void 0:arguments[4]},function(){return 0===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:arguments.length<=2?void 0:arguments[2]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:2===(arguments.length<=0?void 0:arguments[0])?arguments.length<=2?void 0:arguments[2]:3===(arguments.length<=0?void 0:arguments[0])?arguments.length<=3?void 0:arguments[3]:arguments.length<=4?void 0:arguments[4]},function(){return 0===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=2?void 0:arguments[2]:arguments.length<=3?void 0:arguments[3]}][n].apply(null,[t].concat(e))};return void 0!==d[a]&&(n=d[a][e],null!==t&&"number"==typeof t&&(r=d[a]["mejs.plural-form"],n=i.apply(null,[n,t,r]))),!n&&d.en&&(n=d.en[e],null!==t&&"number"==typeof t&&(r=d.en["mejs.plural-form"],n=i.apply(null,[n,t,r]))),n=n||e,null!==t&&"number"==typeof t&&(n=n.replace("%1",t)),(0,s.escapeHTML)(n)}return e}};i.default.i18n=d,"undefined"!=typeof mejsL10n&&i.default.i18n.language(mejsL10n.language,mejsL10n.strings),n.default=d},{18:18,7:7,9:9}],6:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var L="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},O=r(e(3)),C=r(e(2)),I=r(e(7)),k=e(18),U=e(19),M=e(8),R=e(16);function r(e){return e&&e.__esModule?e:{default:e}}var a=function e(t,n,r){var c=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e);var f=this;r=Array.isArray(r)?r:null,f.defaults={renderers:[],fakeNodeName:"mediaelementwrapper",pluginPath:"build/",shimScriptAccess:"sameDomain"},n=Object.assign(f.defaults,n),f.mediaElement=C.default.createElement(n.fakeNodeName);var a=t,i=!1;if("string"==typeof t?f.mediaElement.originalNode=C.default.getElementById(t):a=(f.mediaElement.originalNode=t).id,void 0===f.mediaElement.originalNode||null===f.mediaElement.originalNode)return null;f.mediaElement.options=n,a=a||"mejs_"+Math.random().toString().slice(2),f.mediaElement.originalNode.setAttribute("id",a+"_from_mejs");var o=f.mediaElement.originalNode.tagName.toLowerCase();-1<["video","audio"].indexOf(o)&&!f.mediaElement.originalNode.getAttribute("preload")&&f.mediaElement.originalNode.setAttribute("preload","none"),f.mediaElement.originalNode.parentNode.insertBefore(f.mediaElement,f.mediaElement.originalNode),f.mediaElement.appendChild(f.mediaElement.originalNode);var l=function(t,e){if("https:"===O.default.location.protocol&&0===t.indexOf("http:")&&R.IS_IOS&&-1<I.default.html5media.mediaTypes.indexOf(e)){var n=new XMLHttpRequest;n.onreadystatechange=function(){if(4===this.readyState&&200===this.status){var e=(O.default.URL||O.default.webkitURL).createObjectURL(this.response);return f.mediaElement.originalNode.setAttribute("src",e),e}return t},n.open("GET",t),n.responseType="blob",n.send()}return t},s=void 0;if(null!==r)s=r;else if(null!==f.mediaElement.originalNode)switch(s=[],f.mediaElement.originalNode.nodeName.toLowerCase()){case"iframe":s.push({type:"",src:f.mediaElement.originalNode.getAttribute("src")});break;case"audio":case"video":var d=f.mediaElement.originalNode.children.length,u=f.mediaElement.originalNode.getAttribute("src");if(u){var m=f.mediaElement.originalNode,p=(0,U.formatType)(u,m.getAttribute("type"));s.push({type:p,src:l(u,p)})}for(var h=0;h<d;h++){var v=f.mediaElement.originalNode.children[h];if("source"===v.tagName.toLowerCase()){var g=v.getAttribute("src"),y=(0,U.formatType)(g,v.getAttribute("type"));s.push({type:y,src:l(g,y)})}}}f.mediaElement.id=a,f.mediaElement.renderers={},f.mediaElement.events={},f.mediaElement.promises=[],f.mediaElement.renderer=null,f.mediaElement.rendererName=null,f.mediaElement.changeRenderer=function(e,t){var n=c,r=2<Object.keys(t[0]).length?t[0]:t[0].src;if(void 0!==n.mediaElement.renderer&&null!==n.mediaElement.renderer&&n.mediaElement.renderer.name===e)return n.mediaElement.renderer.pause(),n.mediaElement.renderer.stop&&n.mediaElement.renderer.stop(),n.mediaElement.renderer.show(),n.mediaElement.renderer.setSrc(r),!0;void 0!==n.mediaElement.renderer&&null!==n.mediaElement.renderer&&(n.mediaElement.renderer.pause(),n.mediaElement.renderer.stop&&n.mediaElement.renderer.stop(),n.mediaElement.renderer.hide());var a=n.mediaElement.renderers[e],i=null;if(null!=a)return a.show(),a.setSrc(r),n.mediaElement.renderer=a,n.mediaElement.rendererName=e,!0;for(var o=n.mediaElement.options.renderers.length?n.mediaElement.options.renderers:M.renderer.order,l=0,s=o.length;l<s;l++){var d=o[l];if(d===e){i=M.renderer.renderers[d];var u=Object.assign(i.options,n.mediaElement.options);return(a=i.create(n.mediaElement,u,t)).name=e,n.mediaElement.renderers[i.name]=a,n.mediaElement.renderer=a,n.mediaElement.rendererName=e,a.show(),!0}}return!1},f.mediaElement.setSize=function(e,t){void 0!==f.mediaElement.renderer&&null!==f.mediaElement.renderer&&f.mediaElement.renderer.setSize(e,t)},f.mediaElement.generateError=function(e,t){e=e||"",t=Array.isArray(t)?t:[];var n=(0,k.createEvent)("error",f.mediaElement);n.message=e,n.urls=t,f.mediaElement.dispatchEvent(n),i=!0};var E=I.default.html5media.properties,b=I.default.html5media.methods,w=function(t,e,n,r){var a=t[e];Object.defineProperty(t,e,{get:function(){return n.apply(t,[a])},set:function(e){return a=r.apply(t,[e])}})},_=function(e){if("src"!==e){var t=""+e.substring(0,1).toUpperCase()+e.substring(1),n=function(){return void 0!==f.mediaElement.renderer&&null!==f.mediaElement.renderer&&"function"==typeof f.mediaElement.renderer["get"+t]?f.mediaElement.renderer["get"+t]():null},r=function(e){void 0!==f.mediaElement.renderer&&null!==f.mediaElement.renderer&&"function"==typeof f.mediaElement.renderer["set"+t]&&f.mediaElement.renderer["set"+t](e)};w(f.mediaElement,e,n,r),f.mediaElement["get"+t]=n,f.mediaElement["set"+t]=r}},S=function(){return void 0!==f.mediaElement.renderer&&null!==f.mediaElement.renderer?f.mediaElement.renderer.getSrc():null},N=function(e){var t=[];if("string"==typeof e)t.push({src:e,type:e?(0,U.getTypeFromFile)(e):""});else if("object"===(void 0===e?"undefined":L(e))&&void 0!==e.src){var n=(0,U.absolutizeUrl)(e.src),r=e.type,a=Object.assign(e,{src:n,type:""!==r&&null!=r||!n?r:(0,U.getTypeFromFile)(n)});t.push(a)}else if(Array.isArray(e))for(var i=0,o=e.length;i<o;i++){var l=(0,U.absolutizeUrl)(e[i].src),s=e[i].type,d=Object.assign(e[i],{src:l,type:""!==s&&null!=s||!l?s:(0,U.getTypeFromFile)(l)});t.push(d)}var u=M.renderer.select(t,f.mediaElement.options.renderers.length?f.mediaElement.options.renderers:[]),c=void 0;if(f.mediaElement.paused||null==f.mediaElement.src||""===f.mediaElement.src||(f.mediaElement.pause(),c=(0,k.createEvent)("pause",f.mediaElement),f.mediaElement.dispatchEvent(c)),f.mediaElement.originalNode.src=t[0].src||"",null!==u||!t[0].src)return!(null==t[0].src||""===t[0].src)?f.mediaElement.changeRenderer(u.rendererName,t):null;f.mediaElement.generateError("No renderer found",t)},j=function(e,t){try{if("play"!==e||"native_dash"!==f.mediaElement.rendererName&&"native_hls"!==f.mediaElement.rendererName&&"vimeo_iframe"!==f.mediaElement.rendererName)f.mediaElement.renderer[e](t);else{var n=f.mediaElement.renderer[e](t);n&&"function"==typeof n.then&&n.catch(function(){f.mediaElement.paused&&setTimeout(function(){var e=f.mediaElement.renderer.play();void 0!==e&&e.catch(function(){f.mediaElement.renderer.paused||f.mediaElement.renderer.pause()})},150)})}}catch(e){f.mediaElement.generateError(e,s)}},A=function(r){f.mediaElement[r]=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return void 0!==f.mediaElement.renderer&&null!==f.mediaElement.renderer&&"function"==typeof f.mediaElement.renderer[r]&&(f.mediaElement.promises.length?Promise.all(f.mediaElement.promises).then(function(){j(r,t)}).catch(function(e){f.mediaElement.generateError(e,s)}):j(r,t)),null}};w(f.mediaElement,"src",S,N),f.mediaElement.getSrc=S,f.mediaElement.setSrc=N;for(var T=0,F=E.length;T<F;T++)_(E[T]);for(var P=0,x=b.length;P<x;P++)A(b[P]);return f.mediaElement.addEventListener=function(e,t){f.mediaElement.events[e]=f.mediaElement.events[e]||[],f.mediaElement.events[e].push(t)},f.mediaElement.removeEventListener=function(e,t){if(!e)return f.mediaElement.events={},!0;var n=f.mediaElement.events[e];if(!n)return!0;if(!t)return f.mediaElement.events[e]=[],!0;for(var r=0;r<n.length;r++)if(n[r]===t)return f.mediaElement.events[e].splice(r,1),!0;return!1},f.mediaElement.dispatchEvent=function(e){var t=f.mediaElement.events[e.type];if(t)for(var n=0;n<t.length;n++)t[n].apply(null,[e])},f.mediaElement.destroy=function(){var e=f.mediaElement.originalNode.cloneNode(!0),t=f.mediaElement.parentElement;e.removeAttribute("id"),e.remove(),f.mediaElement.remove(),t.appendChild(e)},s.length&&(f.mediaElement.src=s),f.mediaElement.promises.length?Promise.all(f.mediaElement.promises).then(function(){f.mediaElement.options.success&&f.mediaElement.options.success(f.mediaElement,f.mediaElement.originalNode)}).catch(function(){i&&f.mediaElement.options.error&&f.mediaElement.options.error(f.mediaElement,f.mediaElement.originalNode)}):(f.mediaElement.options.success&&f.mediaElement.options.success(f.mediaElement,f.mediaElement.originalNode),i&&f.mediaElement.options.error&&f.mediaElement.options.error(f.mediaElement,f.mediaElement.originalNode)),f.mediaElement};O.default.MediaElement=a,I.default.MediaElement=a,n.default=a},{16:16,18:18,19:19,2:2,3:3,7:7,8:8}],7:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r,a=e(3);var i={version:"4.2.17",html5media:{properties:["volume","src","currentTime","muted","duration","paused","ended","buffered","error","networkState","readyState","seeking","seekable","currentSrc","preload","bufferedBytes","bufferedTime","initialTime","startOffsetTime","defaultPlaybackRate","playbackRate","played","autoplay","loop","controls"],readOnlyProperties:["duration","paused","ended","buffered","error","networkState","readyState","seeking","seekable"],methods:["load","play","pause","canPlayType"],events:["loadstart","durationchange","loadedmetadata","loadeddata","progress","canplay","canplaythrough","suspend","abort","error","emptied","stalled","play","playing","pause","waiting","seeking","seeked","timeupdate","ended","ratechange","volumechange"],mediaTypes:["audio/mp3","audio/ogg","audio/oga","audio/wav","audio/x-wav","audio/wave","audio/x-pn-wav","audio/mpeg","audio/mp4","video/mp4","video/webm","video/ogg","video/ogv"]}};((r=a)&&r.__esModule?r:{default:r}).default.mejs=i,n.default=i},{3:3}],8:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.renderer=void 0;var r,a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i=function(){function r(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(e,t,n){return t&&r(e.prototype,t),n&&r(e,n),e}}(),o=e(7),l=(r=o)&&r.__esModule?r:{default:r};var s=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.renderers={},this.order=[]}return i(e,[{key:"add",value:function(e){if(void 0===e.name)throw new TypeError("renderer must contain at least `name` property");this.renderers[e.name]=e,this.order.push(e.name)}},{key:"select",value:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:[],n=t.length;if(t=t.length?t:this.order,!n){var r=[/^(html5|native)/i,/^flash/i,/iframe$/i],a=function(e){for(var t=0,n=r.length;t<n;t++)if(r[t].test(e))return t;return r.length};t.sort(function(e,t){return a(e)-a(t)})}for(var i=0,o=t.length;i<o;i++){var l=t[i],s=this.renderers[l];if(null!=s)for(var d=0,u=e.length;d<u;d++)if("function"==typeof s.canPlayType&&"string"==typeof e[d].type&&s.canPlayType(e[d].type))return{rendererName:s.name,src:e[d].src}}return null}},{key:"order",set:function(e){if(!Array.isArray(e))throw new TypeError("order must be an array of strings.");this._order=e},get:function(){return this._order}},{key:"renderers",set:function(e){if(null!==e&&"object"!==(void 0===e?"undefined":a(e)))throw new TypeError("renderers must be an array of objects.");this._renderers=e},get:function(){return this._renderers}}]),e}(),d=n.renderer=new s;l.default.Renderers=d},{7:7}],9:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0});n.EN={"mejs.plural-form":1,"mejs.download-file":"Download File","mejs.install-flash":"You are using a browser that does not have Flash player enabled or installed. Please turn on your Flash player plugin or download the latest version from https://get.adobe.com/flashplayer/","mejs.fullscreen":"Fullscreen","mejs.play":"Play","mejs.pause":"Pause","mejs.time-slider":"Time Slider","mejs.time-help-text":"Use Left/Right Arrow keys to advance one second, Up/Down arrows to advance ten seconds.","mejs.live-broadcast":"Live Broadcast","mejs.volume-help-text":"Use Up/Down Arrow keys to increase or decrease volume.","mejs.unmute":"Unmute","mejs.mute":"Mute","mejs.volume-slider":"Volume Slider","mejs.video-player":"Video Player","mejs.audio-player":"Audio Player","mejs.captions-subtitles":"Captions/Subtitles","mejs.captions-chapters":"Chapters","mejs.none":"None","mejs.afrikaans":"Afrikaans","mejs.albanian":"Albanian","mejs.arabic":"Arabic","mejs.belarusian":"Belarusian","mejs.bulgarian":"Bulgarian","mejs.catalan":"Catalan","mejs.chinese":"Chinese","mejs.chinese-simplified":"Chinese (Simplified)","mejs.chinese-traditional":"Chinese (Traditional)","mejs.croatian":"Croatian","mejs.czech":"Czech","mejs.danish":"Danish","mejs.dutch":"Dutch","mejs.english":"English","mejs.estonian":"Estonian","mejs.filipino":"Filipino","mejs.finnish":"Finnish","mejs.french":"French","mejs.galician":"Galician","mejs.german":"German","mejs.greek":"Greek","mejs.haitian-creole":"Haitian Creole","mejs.hebrew":"Hebrew","mejs.hindi":"Hindi","mejs.hungarian":"Hungarian","mejs.icelandic":"Icelandic","mejs.indonesian":"Indonesian","mejs.irish":"Irish","mejs.italian":"Italian","mejs.japanese":"Japanese","mejs.korean":"Korean","mejs.latvian":"Latvian","mejs.lithuanian":"Lithuanian","mejs.macedonian":"Macedonian","mejs.malay":"Malay","mejs.maltese":"Maltese","mejs.norwegian":"Norwegian","mejs.persian":"Persian","mejs.polish":"Polish","mejs.portuguese":"Portuguese","mejs.romanian":"Romanian","mejs.russian":"Russian","mejs.serbian":"Serbian","mejs.slovak":"Slovak","mejs.slovenian":"Slovenian","mejs.spanish":"Spanish","mejs.swahili":"Swahili","mejs.swedish":"Swedish","mejs.tagalog":"Tagalog","mejs.thai":"Thai","mejs.turkish":"Turkish","mejs.ukrainian":"Ukrainian","mejs.vietnamese":"Vietnamese","mejs.welsh":"Welsh","mejs.yiddish":"Yiddish"}},{}],10:[function(e,t,n){"use strict";var b="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},w=o(e(3)),_=o(e(7)),S=e(8),N=e(18),r=e(19),a=e(16),i=e(17);function o(e){return e&&e.__esModule?e:{default:e}}var j={promise:null,load:function(e){return"undefined"!=typeof dashjs?j.promise=new Promise(function(e){e()}).then(function(){j._createPlayer(e)}):(e.options.path="string"==typeof e.options.path?e.options.path:"https://cdn.dashjs.org/latest/dash.all.min.js",j.promise=j.promise||(0,i.loadScript)(e.options.path),j.promise.then(function(){j._createPlayer(e)})),j.promise},_createPlayer:function(e){var t=dashjs.MediaPlayer().create();return w.default["__ready__"+e.id](t),t}},l={name:"native_dash",options:{prefix:"native_dash",dash:{path:"https://cdn.dashjs.org/latest/dash.all.min.js",debug:!1,drm:{},robustnessLevel:""}},canPlayType:function(e){return a.HAS_MSE&&-1<["application/dash+xml"].indexOf(e.toLowerCase())},create:function(l,s,e){var t=l.originalNode,i=l.id+"_"+s.prefix,o=t.autoplay,n=t.children,d=null,u=null;t.removeAttribute("type");for(var r=0,a=n.length;r<a;r++)n[r].removeAttribute("type");d=t.cloneNode(!0),s=Object.assign(s,l.options);for(var c=_.default.html5media.properties,f=_.default.html5media.events.concat(["click","mouseover","mouseout"]).filter(function(e){return"error"!==e}),m=function(e){var t=(0,N.createEvent)(e.type,l);l.dispatchEvent(t)},p=function(a){var e=""+a.substring(0,1).toUpperCase()+a.substring(1);d["get"+e]=function(){return null!==u?d[a]:null},d["set"+e]=function(e){if(-1===_.default.html5media.readOnlyProperties.indexOf(a))if("src"===a){var t="object"===(void 0===e?"undefined":b(e))&&e.src?e.src:e;if(d[a]=t,null!==u){u.reset();for(var n=0,r=f.length;n<r;n++)d.removeEventListener(f[n],m);u=j._createPlayer({options:s.dash,id:i}),e&&"object"===(void 0===e?"undefined":b(e))&&"object"===b(e.drm)&&(u.setProtectionData(e.drm),(0,N.isString)(s.dash.robustnessLevel)&&s.dash.robustnessLevel&&u.getProtectionController().setRobustnessLevel(s.dash.robustnessLevel)),u.attachSource(t),o&&u.play()}}else d[a]=e}},h=0,v=c.length;h<v;h++)p(c[h]);if(w.default["__ready__"+i]=function(e){l.dashPlayer=u=e;for(var t,n=dashjs.MediaPlayer.events,r=0,a=f.length;r<a;r++)"loadedmetadata"===(t=f[r])&&(u.initialize(),u.attachView(d),u.setAutoPlay(!1),"object"!==b(s.dash.drm)||_.default.Utils.isObjectEmpty(s.dash.drm)||(u.setProtectionData(s.dash.drm),(0,N.isString)(s.dash.robustnessLevel)&&s.dash.robustnessLevel&&u.getProtectionController().setRobustnessLevel(s.dash.robustnessLevel)),u.attachSource(d.getSrc())),d.addEventListener(t,m);var i=function(e){if("error"===e.type.toLowerCase())l.generateError(e.message,d.src),console.error(e);else{var t=(0,N.createEvent)(e.type,l);t.data=e,l.dispatchEvent(t)}};for(var o in n)n.hasOwnProperty(o)&&u.on(n[o],function(e){return i(e)})},e&&0<e.length)for(var g=0,y=e.length;g<y;g++)if(S.renderer.renderers[s.prefix].canPlayType(e[g].type)){d.setAttribute("src",e[g].src),void 0!==e[g].drm&&(s.dash.drm=e[g].drm);break}d.setAttribute("id",i),t.parentNode.insertBefore(d,t),t.autoplay=!1,t.style.display="none",d.setSize=function(e,t){return d.style.width=e+"px",d.style.height=t+"px",d},d.hide=function(){return d.pause(),d.style.display="none",d},d.show=function(){return d.style.display="",d},d.destroy=function(){null!==u&&u.reset()};var E=(0,N.createEvent)("rendererready",d);return l.dispatchEvent(E),l.promises.push(j.load({options:s.dash,id:i})),d}};r.typeChecks.push(function(e){return~e.toLowerCase().indexOf(".mpd")?"application/dash+xml":null}),S.renderer.add(l)},{16:16,17:17,18:18,19:19,3:3,7:7,8:8}],11:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.PluginDetector=void 0;var d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},A=r(e(3)),T=r(e(2)),F=r(e(7)),P=r(e(5)),x=e(8),L=e(18),O=e(16),C=e(19);function r(e){return e&&e.__esModule?e:{default:e}}var i=n.PluginDetector={plugins:[],hasPluginVersion:function(e,t){var n=i.plugins[e];return t[1]=t[1]||0,t[2]=t[2]||0,n[0]>t[0]||n[0]===t[0]&&n[1]>t[1]||n[0]===t[0]&&n[1]===t[1]&&n[2]>=t[2]},addPlugin:function(e,t,n,r,a){i.plugins[e]=i.detectPlugin(t,n,r,a)},detectPlugin:function(e,t,n,r){var a=[0,0,0],i=void 0,o=void 0;if(null!==O.NAV.plugins&&void 0!==O.NAV.plugins&&"object"===d(O.NAV.plugins[e])){if((i=O.NAV.plugins[e].description)&&(void 0===O.NAV.mimeTypes||!O.NAV.mimeTypes[t]||O.NAV.mimeTypes[t].enabledPlugin))for(var l=0,s=(a=i.replace(e,"").replace(/^\s+/,"").replace(/\sr/gi,".").split(".")).length;l<s;l++)a[l]=parseInt(a[l].match(/\d+/),10)}else if(void 0!==A.default.ActiveXObject)try{(o=new ActiveXObject(n))&&(a=r(o))}catch(e){}return a}};i.addPlugin("flash","Shockwave Flash","application/x-shockwave-flash","ShockwaveFlash.ShockwaveFlash",function(e){var t=[],n=e.GetVariable("$version");return n&&(n=n.split(" ")[1].split(","),t=[parseInt(n[0],10),parseInt(n[1],10),parseInt(n[2],10)]),t});var a={create:function(e,t,n){var i={},r=!1;i.options=t,i.id=e.id+"_"+i.options.prefix,i.mediaElement=e,i.flashState={},i.flashApi=null,i.flashApiStack=[];for(var a=F.default.html5media.properties,o=function(t){i.flashState[t]=null;var e=""+t.substring(0,1).toUpperCase()+t.substring(1);i["get"+e]=function(){if(null!==i.flashApi){if("function"==typeof i.flashApi["get_"+t]){var e=i.flashApi["get_"+t]();return"buffered"===t?{start:function(){return 0},end:function(){return e},length:1}:e}return null}return null},i["set"+e]=function(e){if("src"===t&&(e=(0,C.absolutizeUrl)(e)),null!==i.flashApi&&void 0!==i.flashApi["set_"+t])try{i.flashApi["set_"+t](e)}catch(e){}else i.flashApiStack.push({type:"set",propName:t,value:e})}},l=0,s=a.length;l<s;l++)o(a[l]);var d=F.default.html5media.methods,u=function(e){i[e]=function(){if(r)if(null!==i.flashApi){if(i.flashApi["fire_"+e])try{i.flashApi["fire_"+e]()}catch(e){}}else i.flashApiStack.push({type:"call",methodName:e})}};d.push("stop");for(var c=0,f=d.length;c<f;c++)u(d[c]);for(var m=["rendererready"],p=0,h=m.length;p<h;p++){var v=(0,L.createEvent)(m[p],i);e.dispatchEvent(v)}A.default["__ready__"+i.id]=function(){if(i.flashReady=!0,i.flashApi=T.default.getElementById("__"+i.id),i.flashApiStack.length)for(var e=0,t=i.flashApiStack.length;e<t;e++){var n=i.flashApiStack[e];if("set"===n.type){var r=n.propName,a=""+r.substring(0,1).toUpperCase()+r.substring(1);i["set"+a](n.value)}else"call"===n.type&&i[n.methodName]()}},A.default["__event__"+i.id]=function(e,t){var n=(0,L.createEvent)(e,i);if(t)try{n.data=JSON.parse(t),n.details.data=JSON.parse(t)}catch(e){n.message=t}i.mediaElement.dispatchEvent(n)},i.flashWrapper=T.default.createElement("div"),-1===["always","sameDomain"].indexOf(i.options.shimScriptAccess)&&(i.options.shimScriptAccess="sameDomain");var g=e.originalNode.autoplay,y=["uid="+i.id,"autoplay="+g,"allowScriptAccess="+i.options.shimScriptAccess,"preload="+(e.originalNode.getAttribute("preload")||"")],E=null!==e.originalNode&&"video"===e.originalNode.tagName.toLowerCase(),b=E?e.originalNode.height:1,w=E?e.originalNode.width:1;e.originalNode.getAttribute("src")&&y.push("src="+e.originalNode.getAttribute("src")),!0===i.options.enablePseudoStreaming&&(y.push("pseudostreamstart="+i.options.pseudoStreamingStartQueryParam),y.push("pseudostreamtype="+i.options.pseudoStreamingType)),i.options.streamDelimiter&&y.push("streamdelimiter="+encodeURIComponent(i.options.streamDelimiter)),i.options.proxyType&&y.push("proxytype="+i.options.proxyType),e.appendChild(i.flashWrapper),e.originalNode.style.display="none";var _=[];if(O.IS_IE||O.IS_EDGE){var S=T.default.createElement("div");i.flashWrapper.appendChild(S),_=O.IS_EDGE?['type="application/x-shockwave-flash"','data="'+i.options.pluginPath+i.options.filename+'"','id="__'+i.id+'"','width="'+w+'"','height="'+b+"'\""]:['classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000"','codebase="//download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab"','id="__'+i.id+'"','width="'+w+'"','height="'+b+'"'],E||_.push('style="clip: rect(0 0 0 0); position: absolute;"'),S.outerHTML="<object "+_.join(" ")+'><param name="movie" value="'+i.options.pluginPath+i.options.filename+"?x="+new Date+'" /><param name="flashvars" value="'+y.join("&amp;")+'" /><param name="quality" value="high" /><param name="bgcolor" value="#000000" /><param name="wmode" value="transparent" /><param name="allowScriptAccess" value="'+i.options.shimScriptAccess+'" /><param name="allowFullScreen" value="true" /><div>'+P.default.t("mejs.install-flash")+"</div></object>"}else _=['id="__'+i.id+'"','name="__'+i.id+'"','play="true"','loop="false"','quality="high"','bgcolor="#000000"','wmode="transparent"','allowScriptAccess="'+i.options.shimScriptAccess+'"','allowFullScreen="true"','type="application/x-shockwave-flash"','pluginspage="//www.macromedia.com/go/getflashplayer"','src="'+i.options.pluginPath+i.options.filename+'"','flashvars="'+y.join("&")+'"'],E?(_.push('width="'+w+'"'),_.push('height="'+b+'"')):_.push('style="position: fixed; left: -9999em; top: -9999em;"'),i.flashWrapper.innerHTML="<embed "+_.join(" ")+">";if(i.flashNode=i.flashWrapper.lastChild,i.hide=function(){r=!1,E&&(i.flashNode.style.display="none")},i.show=function(){r=!0,E&&(i.flashNode.style.display="")},i.setSize=function(e,t){i.flashNode.style.width=e+"px",i.flashNode.style.height=t+"px",null!==i.flashApi&&"function"==typeof i.flashApi.fire_setSize&&i.flashApi.fire_setSize(e,t)},i.destroy=function(){i.flashNode.remove()},n&&0<n.length)for(var N=0,j=n.length;N<j;N++)if(x.renderer.renderers[t.prefix].canPlayType(n[N].type)){i.setSrc(n[N].src);break}return i}};if(i.hasPluginVersion("flash",[10,0,0])){C.typeChecks.push(function(e){return(e=e.toLowerCase()).startsWith("rtmp")?~e.indexOf(".mp3")?"audio/rtmp":"video/rtmp":/\.og(a|g)/i.test(e)?"audio/ogg":~e.indexOf(".m3u8")?"application/x-mpegURL":~e.indexOf(".mpd")?"application/dash+xml":~e.indexOf(".flv")?"video/flv":null});var o={name:"flash_video",options:{prefix:"flash_video",filename:"mediaelement-flash-video.swf",enablePseudoStreaming:!1,pseudoStreamingStartQueryParam:"start",pseudoStreamingType:"byte",proxyType:"",streamDelimiter:""},canPlayType:function(e){return~["video/mp4","video/rtmp","audio/rtmp","rtmp/mp4","audio/mp4","video/flv","video/x-flv"].indexOf(e.toLowerCase())},create:a.create};x.renderer.add(o);var l={name:"flash_hls",options:{prefix:"flash_hls",filename:"mediaelement-flash-video-hls.swf"},canPlayType:function(e){return~["application/x-mpegurl","application/vnd.apple.mpegurl","audio/mpegurl","audio/hls","video/hls"].indexOf(e.toLowerCase())},create:a.create};x.renderer.add(l);var s={name:"flash_dash",options:{prefix:"flash_dash",filename:"mediaelement-flash-video-mdash.swf"},canPlayType:function(e){return~["application/dash+xml"].indexOf(e.toLowerCase())},create:a.create};x.renderer.add(s);var u={name:"flash_audio",options:{prefix:"flash_audio",filename:"mediaelement-flash-audio.swf"},canPlayType:function(e){return~["audio/mp3"].indexOf(e.toLowerCase())},create:a.create};x.renderer.add(u);var c={name:"flash_audio_ogg",options:{prefix:"flash_audio_ogg",filename:"mediaelement-flash-audio-ogg.swf"},canPlayType:function(e){return~["audio/ogg","audio/oga","audio/ogv"].indexOf(e.toLowerCase())},create:a.create};x.renderer.add(c)}},{16:16,18:18,19:19,2:2,3:3,5:5,7:7,8:8}],12:[function(e,t,n){"use strict";var y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},E=o(e(3)),b=o(e(7)),w=e(8),_=e(18),r=e(16),a=e(19),i=e(17);function o(e){return e&&e.__esModule?e:{default:e}}var S={promise:null,load:function(e){return"undefined"!=typeof flvjs?S.promise=new Promise(function(e){e()}).then(function(){S._createPlayer(e)}):(e.options.path="string"==typeof e.options.path?e.options.path:"https://cdn.jsdelivr.net/npm/flv.js@latest",S.promise=S.promise||(0,i.loadScript)(e.options.path),S.promise.then(function(){S._createPlayer(e)})),S.promise},_createPlayer:function(e){flvjs.LoggingControl.enableDebug=e.options.debug,flvjs.LoggingControl.enableVerbose=e.options.debug;var t=flvjs.createPlayer(e.options,e.configs);return E.default["__ready__"+e.id](t),t}},l={name:"native_flv",options:{prefix:"native_flv",flv:{path:"https://cdn.jsdelivr.net/npm/flv.js@latest",cors:!0,debug:!1}},canPlayType:function(e){return r.HAS_MSE&&-1<["video/x-flv","video/flv"].indexOf(e.toLowerCase())},create:function(l,o,e){var t=l.originalNode,s=l.id+"_"+o.prefix,d=null,u=null;d=t.cloneNode(!0),o=Object.assign(o,l.options);for(var n=b.default.html5media.properties,c=b.default.html5media.events.concat(["click","mouseover","mouseout"]).filter(function(e){return"error"!==e}),f=function(e){var t=(0,_.createEvent)(e.type,l);l.dispatchEvent(t)},r=function(i){var e=""+i.substring(0,1).toUpperCase()+i.substring(1);d["get"+e]=function(){return null!==u?d[i]:null},d["set"+e]=function(e){if(-1===b.default.html5media.readOnlyProperties.indexOf(i))if("src"===i){if(d[i]="object"===(void 0===e?"undefined":y(e))&&e.src?e.src:e,null!==u){var t={type:"flv"};t.url=e,t.cors=o.flv.cors,t.debug=o.flv.debug,t.path=o.flv.path;var n=o.flv.configs;u.destroy();for(var r=0,a=c.length;r<a;r++)d.removeEventListener(c[r],f);(u=S._createPlayer({options:t,configs:n,id:s})).attachMediaElement(d),u.load()}}else d[i]=e}},a=0,i=n.length;a<i;a++)r(n[a]);if(E.default["__ready__"+s]=function(e){l.flvPlayer=u=e;for(var t,a=flvjs.Events,n=0,r=c.length;n<r;n++)"loadedmetadata"===(t=c[n])&&(u.unload(),u.detachMediaElement(),u.attachMediaElement(d),u.load()),d.addEventListener(t,f);var i=function(r){a.hasOwnProperty(r)&&u.on(a[r],function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t){if("error"===e){var n=t[0]+": "+t[1]+" "+t[2].msg;l.generateError(n,d.src)}else{var r=(0,_.createEvent)(e,l);r.data=t,l.dispatchEvent(r)}}(a[r],t)})};for(var o in a)i(o)},e&&0<e.length)for(var m=0,p=e.length;m<p;m++)if(w.renderer.renderers[o.prefix].canPlayType(e[m].type)){d.setAttribute("src",e[m].src);break}d.setAttribute("id",s),t.parentNode.insertBefore(d,t),t.autoplay=!1,t.style.display="none";var h={type:"flv"};h.url=d.src,h.cors=o.flv.cors,h.debug=o.flv.debug,h.path=o.flv.path;var v=o.flv.configs;d.setSize=function(e,t){return d.style.width=e+"px",d.style.height=t+"px",d},d.hide=function(){return null!==u&&u.pause(),d.style.display="none",d},d.show=function(){return d.style.display="",d},d.destroy=function(){null!==u&&u.destroy()};var g=(0,_.createEvent)("rendererready",d);return l.dispatchEvent(g),l.promises.push(S.load({options:h,configs:v,id:s})),d}};a.typeChecks.push(function(e){return~e.toLowerCase().indexOf(".flv")?"video/flv":null}),w.renderer.add(l)},{16:16,17:17,18:18,19:19,3:3,7:7,8:8}],13:[function(e,t,n){"use strict";var y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},E=o(e(3)),b=o(e(7)),w=e(8),_=e(18),r=e(16),a=e(19),i=e(17);function o(e){return e&&e.__esModule?e:{default:e}}var S={promise:null,load:function(e){return"undefined"!=typeof Hls?S.promise=new Promise(function(e){e()}).then(function(){S._createPlayer(e)}):(e.options.path="string"==typeof e.options.path?e.options.path:"https://cdn.jsdelivr.net/npm/hls.js@latest",S.promise=S.promise||(0,i.loadScript)(e.options.path),S.promise.then(function(){S._createPlayer(e)})),S.promise},_createPlayer:function(e){var t=new Hls(e.options);return E.default["__ready__"+e.id](t),t}},l={name:"native_hls",options:{prefix:"native_hls",hls:{path:"https://cdn.jsdelivr.net/npm/hls.js@latest",autoStartLoad:!1,debug:!1}},canPlayType:function(e){return r.HAS_MSE&&-1<["application/x-mpegurl","application/vnd.apple.mpegurl","audio/mpegurl","audio/hls","video/hls"].indexOf(e.toLowerCase())},create:function(d,a,u){var e=d.originalNode,i=d.id+"_"+a.prefix,t=e.getAttribute("preload"),n=e.autoplay,c=null,f=null,m=0,p=u.length;f=e.cloneNode(!0),(a=Object.assign(a,d.options)).hls.autoStartLoad=t&&"none"!==t||n;for(var r=b.default.html5media.properties,h=b.default.html5media.events.concat(["click","mouseover","mouseout"]).filter(function(e){return"error"!==e}),v=function(e){var t=(0,_.createEvent)(e.type,d);d.dispatchEvent(t)},o=function(r){var e=""+r.substring(0,1).toUpperCase()+r.substring(1);f["get"+e]=function(){return null!==c?f[r]:null},f["set"+e]=function(e){if(-1===b.default.html5media.readOnlyProperties.indexOf(r))if("src"===r){if(f[r]="object"===(void 0===e?"undefined":y(e))&&e.src?e.src:e,null!==c){c.destroy();for(var t=0,n=h.length;t<n;t++)f.removeEventListener(h[t],v);(c=S._createPlayer({options:a.hls,id:i})).loadSource(e),c.attachMedia(f)}}else f[r]=e}},l=0,s=r.length;l<s;l++)o(r[l]);if(E.default["__ready__"+i]=function(e){d.hlsPlayer=c=e;for(var a=Hls.Events,t=function(e){if("loadedmetadata"===e){var t=d.originalNode.src;c.detachMedia(),c.loadSource(t),c.attachMedia(f)}f.addEventListener(e,v)},n=0,r=h.length;n<r;n++)t(h[n]);var l=void 0,s=void 0,i=function(r){a.hasOwnProperty(r)&&c.on(a[r],function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t){if("hlsError"===e&&(console.warn(t),(t=t[1]).fatal))switch(t.type){case"mediaError":var n=(new Date).getTime();if(!l||3e3<n-l)l=(new Date).getTime(),c.recoverMediaError();else if(!s||3e3<n-s)s=(new Date).getTime(),console.warn("Attempting to swap Audio Codec and recover from media error"),c.swapAudioCodec(),c.recoverMediaError();else{var r="Cannot recover, last media error recovery failed";d.generateError(r,f.src),console.error(r)}break;case"networkError":if("manifestLoadError"===t.details)if(m<p&&void 0!==u[m+1])f.setSrc(u[m++].src),f.load(),f.play();else{var a="Network error";d.generateError(a,u),console.error(a)}else{var i="Network error";d.generateError(i,u),console.error(i)}break;default:c.destroy()}else{var o=(0,_.createEvent)(e,d);o.data=t,d.dispatchEvent(o)}}(a[r],t)})};for(var o in a)i(o)},0<p)for(;m<p;m++)if(w.renderer.renderers[a.prefix].canPlayType(u[m].type)){f.setAttribute("src",u[m].src);break}"auto"===t||n||(f.addEventListener("play",function(){null!==c&&c.startLoad()}),f.addEventListener("pause",function(){null!==c&&c.stopLoad()})),f.setAttribute("id",i),e.parentNode.insertBefore(f,e),e.autoplay=!1,e.style.display="none",f.setSize=function(e,t){return f.style.width=e+"px",f.style.height=t+"px",f},f.hide=function(){return f.pause(),f.style.display="none",f},f.show=function(){return f.style.display="",f},f.destroy=function(){null!==c&&(c.stopLoad(),c.destroy())};var g=(0,_.createEvent)("rendererready",f);return d.dispatchEvent(g),d.promises.push(S.load({options:a.hls,id:i})),f}};a.typeChecks.push(function(e){return~e.toLowerCase().indexOf(".m3u8")?"application/x-mpegURL":null}),w.renderer.add(l)},{16:16,17:17,18:18,19:19,3:3,7:7,8:8}],14:[function(e,t,n){"use strict";var r=i(e(3)),g=i(e(2)),y=i(e(7)),E=e(8),b=e(18),a=e(16);function i(e){return e&&e.__esModule?e:{default:e}}var o={name:"html5",options:{prefix:"html5"},canPlayType:function(e){var t=g.default.createElement("video");return a.IS_ANDROID&&/\/mp(3|4)$/i.test(e)||~["application/x-mpegurl","vnd.apple.mpegurl","audio/mpegurl","audio/hls","video/hls"].indexOf(e.toLowerCase())&&a.SUPPORTS_NATIVE_HLS?"yes":t.canPlayType?t.canPlayType(e.toLowerCase()).replace(/no/,""):""},create:function(n,e,t){var r=n.id+"_"+e.prefix,a=!1,i=null;void 0===n.originalNode||null===n.originalNode?(i=g.default.createElement("audio"),n.appendChild(i)):i=n.originalNode,i.setAttribute("id",r);for(var o=y.default.html5media.properties,l=function(t){var e=""+t.substring(0,1).toUpperCase()+t.substring(1);i["get"+e]=function(){return i[t]},i["set"+e]=function(e){-1===y.default.html5media.readOnlyProperties.indexOf(t)&&(i[t]=e)}},s=0,d=o.length;s<d;s++)l(o[s]);for(var u,c=y.default.html5media.events.concat(["click","mouseover","mouseout"]).filter(function(e){return"error"!==e}),f=0,m=c.length;f<m;f++)u=c[f],i.addEventListener(u,function(e){if(a){var t=(0,b.createEvent)(e.type,e.target);n.dispatchEvent(t)}});i.setSize=function(e,t){return i.style.width=e+"px",i.style.height=t+"px",i},i.hide=function(){return a=!1,i.style.display="none",i},i.show=function(){return a=!0,i.style.display="",i};var p=0,h=t.length;if(0<h)for(;p<h;p++)if(E.renderer.renderers[e.prefix].canPlayType(t[p].type)){i.setAttribute("src",t[p].src);break}i.addEventListener("error",function(e){e&&e.target&&e.target.error&&4===e.target.error.code&&a&&(p<h&&void 0!==t[p+1]?(i.src=t[p++].src,i.load(),i.play()):n.generateError("Media error: Format(s) not supported or source(s) not found",t))});var v=(0,b.createEvent)("rendererready",i);return n.dispatchEvent(v),i}};r.default.HtmlMediaElement=y.default.HtmlMediaElement=o,E.renderer.add(o)},{16:16,18:18,2:2,3:3,7:7,8:8}],15:[function(e,t,n){"use strict";var S=o(e(3)),N=o(e(2)),j=o(e(7)),r=e(8),A=e(18),a=e(19),i=e(17);function o(e){return e&&e.__esModule?e:{default:e}}var T={isIframeStarted:!1,isIframeLoaded:!1,iframeQueue:[],enqueueIframe:function(e){T.isLoaded="undefined"!=typeof YT&&YT.loaded,T.isLoaded?T.createIframe(e):(T.loadIframeApi(),T.iframeQueue.push(e))},loadIframeApi:function(){T.isIframeStarted||((0,i.loadScript)("https://www.youtube.com/player_api"),T.isIframeStarted=!0)},iFrameReady:function(){for(T.isLoaded=!0,T.isIframeLoaded=!0;0<T.iframeQueue.length;){var e=T.iframeQueue.pop();T.createIframe(e)}},createIframe:function(e){return new YT.Player(e.containerId,e)},getYouTubeId:function(e){var t="";return 0<e.indexOf("?")?""===(t=T.getYouTubeIdFromParam(e))&&(t=T.getYouTubeIdFromUrl(e)):t=T.getYouTubeIdFromUrl(e),(t=t.substring(t.lastIndexOf("/")+1).split("?"))[0]},getYouTubeIdFromParam:function(e){if(null==e||!e.trim().length)return null;for(var t=e.split("?")[1].split("&"),n="",r=0,a=t.length;r<a;r++){var i=t[r].split("=");if("v"===i[0]){n=i[1];break}}return n},getYouTubeIdFromUrl:function(e){return null!=e&&e.trim().length?(e=e.split("?")[0]).substring(e.lastIndexOf("/")+1):null},getYouTubeNoCookieUrl:function(e){if(null==e||!e.trim().length||-1===e.indexOf("//www.youtube"))return e;var t=e.split("/");return t[2]=t[2].replace(".com","-nocookie.com"),t.join("/")}},l={name:"youtube_iframe",options:{prefix:"youtube_iframe",youtube:{autoplay:0,controls:0,disablekb:1,end:0,loop:0,modestbranding:0,playsinline:0,rel:0,showinfo:0,start:0,iv_load_policy:3,nocookie:!1,imageQuality:null}},canPlayType:function(e){return~["video/youtube","video/x-youtube"].indexOf(e.toLowerCase())},create:function(p,n,r){var h={},v=[],g=null,i=!0,o=!1,y=null;h.options=n,h.id=p.id+"_"+n.prefix,h.mediaElement=p;for(var e=j.default.html5media.properties,t=function(a){var e=""+a.substring(0,1).toUpperCase()+a.substring(1);h["get"+e]=function(){if(null!==g){switch(a){case"currentTime":return g.getCurrentTime();case"duration":return g.getDuration();case"volume":return g.getVolume()/100;case"playbackRate":return g.getPlaybackRate();case"paused":return i;case"ended":return o;case"muted":return g.isMuted();case"buffered":var e=g.getVideoLoadedFraction(),t=g.getDuration();return{start:function(){return 0},end:function(){return e*t},length:1};case"src":return g.getVideoUrl();case"readyState":return 4}return null}return null},h["set"+e]=function(e){if(null!==g)switch(a){case"src":var t="string"==typeof e?e:e[0].src,n=T.getYouTubeId(t);p.originalNode.autoplay?g.loadVideoById(n):g.cueVideoById(n);break;case"currentTime":g.seekTo(e);break;case"muted":e?g.mute():g.unMute(),setTimeout(function(){var e=(0,A.createEvent)("volumechange",h);p.dispatchEvent(e)},50);break;case"volume":e,g.setVolume(100*e),setTimeout(function(){var e=(0,A.createEvent)("volumechange",h);p.dispatchEvent(e)},50);break;case"playbackRate":g.setPlaybackRate(e),setTimeout(function(){var e=(0,A.createEvent)("ratechange",h);p.dispatchEvent(e)},50);break;case"readyState":var r=(0,A.createEvent)("canplay",h);p.dispatchEvent(r)}else v.push({type:"set",propName:a,value:e})}},a=0,l=e.length;a<l;a++)t(e[a]);for(var s=j.default.html5media.methods,d=function(e){h[e]=function(){if(null!==g)switch(e){case"play":return i=!1,g.playVideo();case"pause":return i=!0,g.pauseVideo();case"load":return null}else v.push({type:"call",methodName:e})}},u=0,c=s.length;u<c;u++)d(s[u]);var f=N.default.createElement("div");f.id=h.id,h.options.youtube.nocookie&&(p.originalNode.src=T.getYouTubeNoCookieUrl(r[0].src)),p.originalNode.parentNode.insertBefore(f,p.originalNode),p.originalNode.style.display="none";var m="audio"===p.originalNode.tagName.toLowerCase(),E=m?"1":p.originalNode.height,b=m?"1":p.originalNode.width,w=T.getYouTubeId(r[0].src),_={id:h.id,containerId:f.id,videoId:w,height:E,width:b,host:h.options.youtube&&h.options.youtube.nocookie?"https://www.youtube-nocookie.com":void 0,playerVars:Object.assign({controls:0,rel:0,disablekb:1,showinfo:0,modestbranding:0,html5:1,iv_load_policy:3},h.options.youtube),origin:S.default.location.host,events:{onReady:function(e){if(p.youTubeApi=g=e.target,p.youTubeState={paused:!0,ended:!1},v.length)for(var t=0,n=v.length;t<n;t++){var r=v[t];if("set"===r.type){var a=r.propName,i=""+a.substring(0,1).toUpperCase()+a.substring(1);h["set"+i](r.value)}else"call"===r.type&&h[r.methodName]()}y=g.getIframe(),p.originalNode.muted&&g.mute();for(var o=["mouseover","mouseout"],l=function(e){var t=(0,A.createEvent)(e.type,h);p.dispatchEvent(t)},s=0,d=o.length;s<d;s++)y.addEventListener(o[s],l,!1);for(var u=["rendererready","loadedmetadata","loadeddata","canplay"],c=0,f=u.length;c<f;c++){var m=(0,A.createEvent)(u[c],h);p.dispatchEvent(m)}},onStateChange:function(e){var t=[];switch(e.data){case-1:t=["loadedmetadata"],i=!0,o=!1;break;case 0:t=["ended"],i=!1,o=!h.options.youtube.loop,h.options.youtube.loop||h.stopInterval();break;case 1:t=["play","playing"],o=i=!1,h.startInterval();break;case 2:t=["pause"],i=!0,o=!1,h.stopInterval();break;case 3:t=["progress"],o=!1;break;case 5:t=["loadeddata","loadedmetadata","canplay"],i=!0,o=!1}for(var n=0,r=t.length;n<r;n++){var a=(0,A.createEvent)(t[n],h);p.dispatchEvent(a)}},onError:function(e){return function(e){var t="";switch(e.data){case 2:t="The request contains an invalid parameter value. Verify that video ID has 11 characters and that contains no invalid characters, such as exclamation points or asterisks.";break;case 5:t="The requested content cannot be played in an HTML5 player or another error related to the HTML5 player has occurred.";break;case 100:t="The video requested was not found. Either video has been removed or has been marked as private.";break;case 101:case 105:t="The owner of the requested video does not allow it to be played in embedded players.";break;default:t="Unknown error."}p.generateError("Code "+e.data+": "+t,r)}(e)}}};return(m||p.originalNode.hasAttribute("playsinline"))&&(_.playerVars.playsinline=1),p.originalNode.controls&&(_.playerVars.controls=1),p.originalNode.autoplay&&(_.playerVars.autoplay=1),p.originalNode.loop&&(_.playerVars.loop=1),(_.playerVars.loop&&1===parseInt(_.playerVars.loop,10)||-1<p.originalNode.src.indexOf("loop="))&&!_.playerVars.playlist&&-1===p.originalNode.src.indexOf("playlist=")&&(_.playerVars.playlist=T.getYouTubeId(p.originalNode.src)),T.enqueueIframe(_),h.onEvent=function(e,t,n){null!=n&&(p.youTubeState=n)},h.setSize=function(e,t){null!==g&&g.setSize(e,t)},h.hide=function(){h.stopInterval(),h.pause(),y&&(y.style.display="none")},h.show=function(){y&&(y.style.display="")},h.destroy=function(){g.destroy()},h.interval=null,h.startInterval=function(){h.interval=setInterval(function(){var e=(0,A.createEvent)("timeupdate",h);p.dispatchEvent(e)},250)},h.stopInterval=function(){h.interval&&clearInterval(h.interval)},h.getPosterUrl=function(){var e=n.youtube.imageQuality,t=T.getYouTubeId(p.originalNode.src);return e&&-1<["default","hqdefault","mqdefault","sddefault","maxresdefault"].indexOf(e)&&t?"https://img.youtube.com/vi/"+t+"/"+e+".jpg":""},h}};S.default.onYouTubePlayerAPIReady=function(){T.iFrameReady()},a.typeChecks.push(function(e){return/\/\/(www\.youtube|youtu\.?be)/i.test(e)?"video/x-youtube":null}),r.renderer.add(l)},{17:17,18:18,19:19,2:2,3:3,7:7,8:8}],16:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.cancelFullScreen=n.requestFullScreen=n.isFullScreen=n.FULLSCREEN_EVENT_NAME=n.HAS_NATIVE_FULLSCREEN_ENABLED=n.HAS_TRUE_NATIVE_FULLSCREEN=n.HAS_IOS_FULLSCREEN=n.HAS_MS_NATIVE_FULLSCREEN=n.HAS_MOZ_NATIVE_FULLSCREEN=n.HAS_WEBKIT_NATIVE_FULLSCREEN=n.HAS_NATIVE_FULLSCREEN=n.SUPPORTS_NATIVE_HLS=n.SUPPORT_PASSIVE_EVENT=n.SUPPORT_POINTER_EVENTS=n.HAS_MSE=n.IS_STOCK_ANDROID=n.IS_SAFARI=n.IS_FIREFOX=n.IS_CHROME=n.IS_EDGE=n.IS_IE=n.IS_ANDROID=n.IS_IOS=n.IS_IPOD=n.IS_IPHONE=n.IS_IPAD=n.UA=n.NAV=void 0;var a=o(e(3)),i=o(e(2)),r=o(e(7));function o(e){return e&&e.__esModule?e:{default:e}}for(var l=n.NAV=a.default.navigator,s=n.UA=l.userAgent.toLowerCase(),d=n.IS_IPAD=/ipad/i.test(s)&&!a.default.MSStream,u=n.IS_IPHONE=/iphone/i.test(s)&&!a.default.MSStream,c=n.IS_IPOD=/ipod/i.test(s)&&!a.default.MSStream,f=(n.IS_IOS=/ipad|iphone|ipod/i.test(s)&&!a.default.MSStream,n.IS_ANDROID=/android/i.test(s)),m=n.IS_IE=/(trident|microsoft)/i.test(l.appName),p=(n.IS_EDGE="msLaunchUri"in l&&!("documentMode"in i.default)),h=n.IS_CHROME=/chrome/i.test(s),v=n.IS_FIREFOX=/firefox/i.test(s),g=n.IS_SAFARI=/safari/i.test(s)&&!h,y=n.IS_STOCK_ANDROID=/^mozilla\/\d+\.\d+\s\(linux;\su;/i.test(s),E=(n.HAS_MSE="MediaSource"in a.default),b=n.SUPPORT_POINTER_EVENTS=function(){var e=i.default.createElement("x"),t=i.default.documentElement,n=a.default.getComputedStyle;if(!("pointerEvents"in e.style))return!1;e.style.pointerEvents="auto",e.style.pointerEvents="x",t.appendChild(e);var r=n&&"auto"===(n(e,"")||{}).pointerEvents;return e.remove(),!!r}(),w=n.SUPPORT_PASSIVE_EVENT=function(){var e=!1;try{var t=Object.defineProperty({},"passive",{get:function(){e=!0}});a.default.addEventListener("test",null,t)}catch(e){}return e}(),_=["source","track","audio","video"],S=void 0,N=0,j=_.length;N<j;N++)S=i.default.createElement(_[N]);var A=n.SUPPORTS_NATIVE_HLS=g||m&&/edge/i.test(s),T=void 0!==S.webkitEnterFullscreen,F=void 0!==S.requestFullscreen;T&&/mac os x 10_5/i.test(s)&&(T=F=!1);var P=void 0!==S.webkitRequestFullScreen,x=void 0!==S.mozRequestFullScreen,L=void 0!==S.msRequestFullscreen,O=P||x||L,C=O,I="",k=void 0,U=void 0,M=void 0;x?C=i.default.mozFullScreenEnabled:L&&(C=i.default.msFullscreenEnabled),h&&(T=!1),O&&(P?I="webkitfullscreenchange":x?I="fullscreenchange":L&&(I="MSFullscreenChange"),n.isFullScreen=k=function(){return x?i.default.mozFullScreen:P?i.default.webkitIsFullScreen:L?null!==i.default.msFullscreenElement:void 0},n.requestFullScreen=U=function(e){P?e.webkitRequestFullScreen():x?e.mozRequestFullScreen():L&&e.msRequestFullscreen()},n.cancelFullScreen=M=function(){P?i.default.webkitCancelFullScreen():x?i.default.mozCancelFullScreen():L&&i.default.msExitFullscreen()});var R=n.HAS_NATIVE_FULLSCREEN=F,V=n.HAS_WEBKIT_NATIVE_FULLSCREEN=P,D=n.HAS_MOZ_NATIVE_FULLSCREEN=x,H=n.HAS_MS_NATIVE_FULLSCREEN=L,q=n.HAS_IOS_FULLSCREEN=T,z=n.HAS_TRUE_NATIVE_FULLSCREEN=O,B=n.HAS_NATIVE_FULLSCREEN_ENABLED=C,Y=n.FULLSCREEN_EVENT_NAME=I;n.isFullScreen=k,n.requestFullScreen=U,n.cancelFullScreen=M,r.default.Features=r.default.Features||{},r.default.Features.isiPad=d,r.default.Features.isiPod=c,r.default.Features.isiPhone=u,r.default.Features.isiOS=r.default.Features.isiPhone||r.default.Features.isiPad,r.default.Features.isAndroid=f,r.default.Features.isIE=m,r.default.Features.isEdge=p,r.default.Features.isChrome=h,r.default.Features.isFirefox=v,r.default.Features.isSafari=g,r.default.Features.isStockAndroid=y,r.default.Features.hasMSE=E,r.default.Features.supportsNativeHLS=A,r.default.Features.supportsPointerEvents=b,r.default.Features.supportsPassiveEvent=w,r.default.Features.hasiOSFullScreen=q,r.default.Features.hasNativeFullscreen=R,r.default.Features.hasWebkitNativeFullScreen=V,r.default.Features.hasMozNativeFullScreen=D,r.default.Features.hasMsNativeFullScreen=H,r.default.Features.hasTrueNativeFullScreen=z,r.default.Features.nativeFullScreenEnabled=B,r.default.Features.fullScreenEventName=Y,r.default.Features.isFullScreen=k,r.default.Features.requestFullScreen=U,r.default.Features.cancelFullScreen=M},{2:2,3:3,7:7}],17:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.removeClass=n.addClass=n.hasClass=void 0,n.loadScript=o,n.offset=l,n.toggleClass=h,n.fadeOut=v,n.fadeIn=g,n.siblings=y,n.visible=E,n.ajax=b;var s=i(e(3)),a=i(e(2)),r=i(e(7));function i(e){return e&&e.__esModule?e:{default:e}}function o(r){return new Promise(function(e,t){var n=a.default.createElement("script");n.src=r,n.async=!0,n.onload=function(){n.remove(),e()},n.onerror=function(){n.remove(),t()},a.default.head.appendChild(n)})}function l(e){var t=e.getBoundingClientRect(),n=s.default.pageXOffset||a.default.documentElement.scrollLeft,r=s.default.pageYOffset||a.default.documentElement.scrollTop;return{top:t.top+r,left:t.left+n}}var d=void 0,u=void 0,c=void 0;"classList"in a.default.documentElement?(d=function(e,t){return void 0!==e.classList&&e.classList.contains(t)},u=function(e,t){return e.classList.add(t)},c=function(e,t){return e.classList.remove(t)}):(d=function(e,t){return new RegExp("\\b"+t+"\\b").test(e.className)},u=function(e,t){f(e,t)||(e.className+=" "+t)},c=function(e,t){e.className=e.className.replace(new RegExp("\\b"+t+"\\b","g"),"")});var f=n.hasClass=d,m=n.addClass=u,p=n.removeClass=c;function h(e,t){f(e,t)?p(e,t):m(e,t)}function v(a){var i=1<arguments.length&&void 0!==arguments[1]?arguments[1]:400,o=arguments[2];a.style.opacity||(a.style.opacity=1);var l=null;s.default.requestAnimationFrame(function e(t){var n=t-(l=l||t),r=parseFloat(1-n/i,2);a.style.opacity=r<0?0:r,i<n?o&&"function"==typeof o&&o():s.default.requestAnimationFrame(e)})}function g(a){var i=1<arguments.length&&void 0!==arguments[1]?arguments[1]:400,o=arguments[2];a.style.opacity||(a.style.opacity=0);var l=null;s.default.requestAnimationFrame(function e(t){var n=t-(l=l||t),r=parseFloat(n/i,2);a.style.opacity=1<r?1:r,i<n?o&&"function"==typeof o&&o():s.default.requestAnimationFrame(e)})}function y(e,t){var n=[];for(e=e.parentNode.firstChild;t&&!t(e)||n.push(e),e=e.nextSibling;);return n}function E(e){return void 0!==e.getClientRects&&"function"===e.getClientRects?!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length):!(!e.offsetWidth&&!e.offsetHeight)}function b(e,t,n,r){var a=s.default.XMLHttpRequest?new XMLHttpRequest:new ActiveXObject("Microsoft.XMLHTTP"),i="application/x-www-form-urlencoded; charset=UTF-8",o=!1,l="*/".concat("*");switch(t){case"text":i="text/plain";break;case"json":i="application/json, text/javascript";break;case"html":i="text/html";break;case"xml":i="application/xml, text/xml"}"application/x-www-form-urlencoded"!==i&&(l=i+", */*; q=0.01"),a&&(a.open("GET",e,!0),a.setRequestHeader("Accept",l),a.onreadystatechange=function(){if(!o&&4===a.readyState)if(200===a.status){o=!0;var e=void 0;switch(t){case"json":e=JSON.parse(a.responseText);break;case"xml":e=a.responseXML;break;default:e=a.responseText}n(e)}else"function"==typeof r&&r(a.status)},a.send())}r.default.Utils=r.default.Utils||{},r.default.Utils.offset=l,r.default.Utils.hasClass=f,r.default.Utils.addClass=m,r.default.Utils.removeClass=p,r.default.Utils.toggleClass=h,r.default.Utils.fadeIn=g,r.default.Utils.fadeOut=v,r.default.Utils.siblings=y,r.default.Utils.visible=E,r.default.Utils.ajax=b,r.default.Utils.loadScript=o},{2:2,3:3,7:7}],18:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.escapeHTML=o,n.debounce=l,n.isObjectEmpty=s,n.splitEvents=d,n.createEvent=u,n.isNodeAfter=c,n.isString=f;var r,a=e(7),i=(r=a)&&r.__esModule?r:{default:r};function o(e){if("string"!=typeof e)throw new Error("Argument passed must be a string");var t={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;"};return e.replace(/[&<>"]/g,function(e){return t[e]})}function l(r,a){var i=this,o=arguments,l=2<arguments.length&&void 0!==arguments[2]&&arguments[2];if("function"!=typeof r)throw new Error("First argument must be a function");if("number"!=typeof a)throw new Error("Second argument must be a numeric value");var s=void 0;return function(){var e=i,t=o,n=l&&!s;clearTimeout(s),s=setTimeout(function(){s=null,l||r.apply(e,t)},a),n&&r.apply(e,t)}}function s(e){return Object.getOwnPropertyNames(e).length<=0}function d(e,n){var r=/^((after|before)print|(before)?unload|hashchange|message|o(ff|n)line|page(hide|show)|popstate|resize|storage)\b/,a={d:[],w:[]};return(e||"").split(" ").forEach(function(e){var t=e+(n?"."+n:"");t.startsWith(".")?(a.d.push(t),a.w.push(t)):a[r.test(e)?"w":"d"].push(t)}),a.d=a.d.join(" "),a.w=a.w.join(" "),a}function u(e,t){if("string"!=typeof e)throw new Error("Event name must be a string");var n=e.match(/([a-z]+\.([a-z]+))/i),r={target:t};return null!==n&&(e=n[1],r.namespace=n[2]),new window.CustomEvent(e,{detail:r})}function c(e,t){return!!(e&&t&&2&e.compareDocumentPosition(t))}function f(e){return"string"==typeof e}i.default.Utils=i.default.Utils||{},i.default.Utils.escapeHTML=o,i.default.Utils.debounce=l,i.default.Utils.isObjectEmpty=s,i.default.Utils.splitEvents=d,i.default.Utils.createEvent=u,i.default.Utils.isNodeAfter=c,i.default.Utils.isString=f},{7:7}],19:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.typeChecks=void 0,n.absolutizeUrl=s,n.formatType=d,n.getMimeFromType=u,n.getTypeFromFile=c,n.getExtension=f,n.normalizeExtension=m;var r,a=e(7),i=(r=a)&&r.__esModule?r:{default:r},o=e(18);var l=n.typeChecks=[];function s(e){if("string"!=typeof e)throw new Error("`url` argument must be a string");var t=document.createElement("div");return t.innerHTML='<a href="'+(0,o.escapeHTML)(e)+'">x</a>',t.firstChild.href}function d(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"";return e&&!t?c(e):t}function u(e){if("string"!=typeof e)throw new Error("`type` argument must be a string");return e&&-1<e.indexOf(";")?e.substr(0,e.indexOf(";")):e}function c(e){if("string"!=typeof e)throw new Error("`url` argument must be a string");for(var t=0,n=l.length;t<n;t++){var r=l[t](e);if(r)return r}var a=m(f(e)),i="video/mp4";return a&&(~["mp4","m4v","ogg","ogv","webm","flv","mpeg"].indexOf(a)?i="video/"+a:"mov"===a?i="video/quicktime":~["mp3","oga","wav","mid","midi"].indexOf(a)&&(i="audio/"+a)),i}function f(e){if("string"!=typeof e)throw new Error("`url` argument must be a string");var t=e.split("?")[0].split("\\").pop().split("/").pop();return~t.indexOf(".")?t.substring(t.lastIndexOf(".")+1):""}function m(e){if("string"!=typeof e)throw new Error("`extension` argument must be a string");switch(e){case"mp4":case"m4v":return"mp4";case"webm":case"webma":case"webmv":return"webm";case"ogg":case"oga":case"ogv":return"ogg";default:return e}}i.default.Utils=i.default.Utils||{},i.default.Utils.typeChecks=l,i.default.Utils.absolutizeUrl=s,i.default.Utils.formatType=d,i.default.Utils.getMimeFromType=u,i.default.Utils.getTypeFromFile=c,i.default.Utils.getExtension=f,i.default.Utils.normalizeExtension=m},{18:18,7:7}],20:[function(e,t,n){"use strict";var r,a=o(e(2)),i=o(e(4));function o(e){return e&&e.__esModule?e:{default:e}}if([Element.prototype,CharacterData.prototype,DocumentType.prototype].forEach(function(e){e.hasOwnProperty("remove")||Object.defineProperty(e,"remove",{configurable:!0,enumerable:!0,writable:!0,value:function(){this.parentNode.removeChild(this)}})}),function(){if("function"==typeof window.CustomEvent)return;function e(e,t){t=t||{bubbles:!1,cancelable:!1,detail:void 0};var n=a.default.createEvent("CustomEvent");return n.initCustomEvent(e,t.bubbles,t.cancelable,t.detail),n}e.prototype=window.Event.prototype,window.CustomEvent=e}(),"function"!=typeof Object.assign&&(Object.assign=function(e){if(null==e)throw new TypeError("Cannot convert undefined or null to object");for(var t=Object(e),n=1,r=arguments.length;n<r;n++){var a=arguments[n];if(null!==a)for(var i in a)Object.prototype.hasOwnProperty.call(a,i)&&(t[i]=a[i])}return t}),String.prototype.startsWith||(String.prototype.startsWith=function(e,t){return t=t||0,this.substr(t,e.length)===e}),Element.prototype.matches||(Element.prototype.matches=Element.prototype.matchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.oMatchesSelector||Element.prototype.webkitMatchesSelector||function(e){for(var t=(this.document||this.ownerDocument).querySelectorAll(e),n=t.length-1;0<=--n&&t.item(n)!==this;);return-1<n}),window.Element&&!Element.prototype.closest&&(Element.prototype.closest=function(e){var t=(this.document||this.ownerDocument).querySelectorAll(e),n=void 0,r=this;do{for(n=t.length;0<=--n&&t.item(n)!==r;);}while(n<0&&(r=r.parentElement));return r}),function(){for(var a=0,e=["ms","moz","webkit","o"],t=0;t<e.length&&!window.requestAnimationFrame;++t)window.requestAnimationFrame=window[e[t]+"RequestAnimationFrame"],window.cancelAnimationFrame=window[e[t]+"CancelAnimationFrame"]||window[e[t]+"CancelRequestAnimationFrame"];window.requestAnimationFrame||(window.requestAnimationFrame=function(e){var t=(new Date).getTime(),n=Math.max(0,16-(t-a)),r=window.setTimeout(function(){e(t+n)},n);return a=t+n,r}),window.cancelAnimationFrame||(window.cancelAnimationFrame=function(e){clearTimeout(e)})}(),/firefox/i.test(navigator.userAgent)){var l=window.getComputedStyle;window.getComputedStyle=function(e,t){var n=l(e,t);return null===n?{getPropertyValue:function(){}}:n}}window.Promise||(window.Promise=i.default),(r=window.Node||window.Element)&&r.prototype&&null===r.prototype.children&&Object.defineProperty(r.prototype,"children",{get:function(){for(var e=0,t=void 0,n=this.childNodes,r=[];t=n[e++];)1===t.nodeType&&r.push(t);return r}})},{2:2,4:4}]},{},[20,6,5,9,14,11,10,12,13,15]);