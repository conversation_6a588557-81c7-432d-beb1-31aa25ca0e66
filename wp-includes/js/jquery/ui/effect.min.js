/*!
 * jQuery UI Effects 1.13.3
 * https://jqueryui.com
 *
 * Copyright OpenJS Foundation and other contributors
 * Released under the MIT license.
 * https://jquery.org/license
 */
!function(t){"use strict";"function"==typeof define&&define.amd?define(["jquery","./jquery-var-for-color","./vendor/jquery-color/jquery.color","./version"],t):t(jQuery)}(function(u){"use strict";var s,o,r,a,c,e,n,i,f,l,d="ui-effects-",h="ui-effects-style",p="ui-effects-animated";function m(t){var e,n,i=t.ownerDocument.defaultView?t.ownerDocument.defaultView.getComputedStyle(t,null):t.currentStyle,o={};if(i&&i.length&&i[0]&&i[i[0]])for(n=i.length;n--;)"string"==typeof i[e=i[n]]&&(o[e.replace(/-([\da-z])/gi,function(t,e){return e.toUpperCase()})]=i[e]);else for(e in i)"string"==typeof i[e]&&(o[e]=i[e]);return o}function g(t,e,n,i){return t={effect:t=u.isPlainObject(t)?(e=t).effect:t},"function"==typeof(e=null==e?{}:e)&&(i=e,n=null,e={}),"number"!=typeof e&&!u.fx.speeds[e]||(i=n,n=e,e={}),"function"==typeof n&&(i=n,n=null),e&&u.extend(t,e),n=n||e.duration,t.duration=u.fx.off?0:"number"==typeof n?n:n in u.fx.speeds?u.fx.speeds[n]:u.fx.speeds._default,t.complete=i||e.complete,t}function v(t){return!t||"number"==typeof t||u.fx.speeds[t]||"string"==typeof t&&!u.effects.effect[t]||"function"==typeof t||"object"==typeof t&&!t.effect}function y(t,e){var n=e.outerWidth(),e=e.outerHeight(),t=/^rect\((-?\d*\.?\d*px|-?\d+%|auto),?\s*(-?\d*\.?\d*px|-?\d+%|auto),?\s*(-?\d*\.?\d*px|-?\d+%|auto),?\s*(-?\d*\.?\d*px|-?\d+%|auto)\)$/.exec(t)||["",0,n,e,0];return{top:parseFloat(t[1])||0,right:"auto"===t[2]?n:parseFloat(t[2]),bottom:"auto"===t[3]?e:parseFloat(t[3]),left:parseFloat(t[4])||0}}return u.effects={effect:{}},a=["add","remove","toggle"],c={border:1,borderBottom:1,borderColor:1,borderLeft:1,borderRight:1,borderTop:1,borderWidth:1,margin:1,padding:1},u.each(["borderLeftStyle","borderRightStyle","borderBottomStyle","borderTopStyle"],function(t,e){u.fx.step[e]=function(t){("none"!==t.end&&!t.setAttr||1===t.pos&&!t.setAttr)&&(jQuery.style(t.elem,e,t.end),t.setAttr=!0)}}),u.fn.addBack||(u.fn.addBack=function(t){return this.add(null==t?this.prevObject:this.prevObject.filter(t))}),u.effects.animateClass=function(o,t,e,n){var s=u.speed(t,e,n);return this.queue(function(){var n=u(this),t=n.attr("class")||"",e=(e=s.children?n.find("*").addBack():n).map(function(){return{el:u(this),start:m(this)}}),i=function(){u.each(a,function(t,e){o[e]&&n[e+"Class"](o[e])})};i(),e=e.map(function(){return this.end=m(this.el[0]),this.diff=function(t,e){var n,i,o={};for(n in e)i=e[n],t[n]===i||c[n]||!u.fx.step[n]&&isNaN(parseFloat(i))||(o[n]=i);return o}(this.start,this.end),this}),n.attr("class",t),e=e.map(function(){var t=this,e=u.Deferred(),n=u.extend({},s,{queue:!1,complete:function(){e.resolve(t)}});return this.el.animate(this.diff,n),e.promise()}),u.when.apply(u,e.get()).done(function(){i(),u.each(arguments,function(){var e=this.el;u.each(this.diff,function(t){e.css(t,"")})}),s.complete.call(n[0])})})},u.fn.extend({addClass:(r=u.fn.addClass,function(t,e,n,i){return e?u.effects.animateClass.call(this,{add:t},e,n,i):r.apply(this,arguments)}),removeClass:(o=u.fn.removeClass,function(t,e,n,i){return 1<arguments.length?u.effects.animateClass.call(this,{remove:t},e,n,i):o.apply(this,arguments)}),toggleClass:(s=u.fn.toggleClass,function(t,e,n,i,o){return"boolean"==typeof e||void 0===e?n?u.effects.animateClass.call(this,e?{add:t}:{remove:t},n,i,o):s.apply(this,arguments):u.effects.animateClass.call(this,{toggle:t},e,n,i)}),switchClass:function(t,e,n,i,o){return u.effects.animateClass.call(this,{add:e,remove:t},n,i,o)}}),u.expr&&u.expr.pseudos&&u.expr.pseudos.animated&&(u.expr.pseudos.animated=(e=u.expr.pseudos.animated,function(t){return!!u(t).data(p)||e(t)})),!1!==u.uiBackCompat&&u.extend(u.effects,{save:function(t,e){for(var n=0,i=e.length;n<i;n++)null!==e[n]&&t.data(d+e[n],t[0].style[e[n]])},restore:function(t,e){for(var n,i=0,o=e.length;i<o;i++)null!==e[i]&&(n=t.data(d+e[i]),t.css(e[i],n))},setMode:function(t,e){return e="toggle"===e?t.is(":hidden")?"show":"hide":e},createWrapper:function(n){if(n.parent().is(".ui-effects-wrapper"))return n.parent();var i={width:n.outerWidth(!0),height:n.outerHeight(!0),float:n.css("float")},t=u("<div></div>").addClass("ui-effects-wrapper").css({fontSize:"100%",background:"transparent",border:"none",margin:0,padding:0}),e={width:n.width(),height:n.height()},o=document.activeElement;try{o.id}catch(t){o=document.body}return n.wrap(t),n[0]!==o&&!u.contains(n[0],o)||u(o).trigger("focus"),t=n.parent(),"static"===n.css("position")?(t.css({position:"relative"}),n.css({position:"relative"})):(u.extend(i,{position:n.css("position"),zIndex:n.css("z-index")}),u.each(["top","left","bottom","right"],function(t,e){i[e]=n.css(e),isNaN(parseInt(i[e],10))&&(i[e]="auto")}),n.css({position:"relative",top:0,left:0,right:"auto",bottom:"auto"})),n.css(e),t.css(i).show()},removeWrapper:function(t){var e=document.activeElement;return t.parent().is(".ui-effects-wrapper")&&(t.parent().replaceWith(t),t[0]!==e&&!u.contains(t[0],e)||u(e).trigger("focus")),t}}),u.extend(u.effects,{version:"1.13.3",define:function(t,e,n){return n||(n=e,e="effect"),u.effects.effect[t]=n,u.effects.effect[t].mode=e,n},scaledDimensions:function(t,e,n){var i;return 0===e?{height:0,width:0,outerHeight:0,outerWidth:0}:(i="horizontal"!==n?(e||100)/100:1,n="vertical"!==n?(e||100)/100:1,{height:t.height()*n,width:t.width()*i,outerHeight:t.outerHeight()*n,outerWidth:t.outerWidth()*i})},clipToBox:function(t){return{width:t.clip.right-t.clip.left,height:t.clip.bottom-t.clip.top,left:t.clip.left,top:t.clip.top}},unshift:function(t,e,n){var i=t.queue();1<e&&i.splice.apply(i,[1,0].concat(i.splice(e,n))),t.dequeue()},saveStyle:function(t){t.data(h,t[0].style.cssText)},restoreStyle:function(t){t[0].style.cssText=t.data(h)||"",t.removeData(h)},mode:function(t,e){t=t.is(":hidden");return"toggle"===e&&(e=t?"show":"hide"),e=(t?"hide"===e:"show"===e)?"none":e},getBaseline:function(t,e){var n,i;switch(t[0]){case"top":n=0;break;case"middle":n=.5;break;case"bottom":n=1;break;default:n=t[0]/e.height}switch(t[1]){case"left":i=0;break;case"center":i=.5;break;case"right":i=1;break;default:i=t[1]/e.width}return{x:i,y:n}},createPlaceholder:function(t){var e,n=t.css("position"),i=t.position();return t.css({marginTop:t.css("marginTop"),marginBottom:t.css("marginBottom"),marginLeft:t.css("marginLeft"),marginRight:t.css("marginRight")}).outerWidth(t.outerWidth()).outerHeight(t.outerHeight()),/^(static|relative)/.test(n)&&(n="absolute",e=u("<"+t[0].nodeName+">").insertAfter(t).css({display:/^(inline|ruby)/.test(t.css("display"))?"inline-block":"block",visibility:"hidden",marginTop:t.css("marginTop"),marginBottom:t.css("marginBottom"),marginLeft:t.css("marginLeft"),marginRight:t.css("marginRight"),float:t.css("float")}).outerWidth(t.outerWidth()).outerHeight(t.outerHeight()).addClass("ui-effects-placeholder"),t.data(d+"placeholder",e)),t.css({position:n,left:i.left,top:i.top}),e},removePlaceholder:function(t){var e=d+"placeholder",n=t.data(e);n&&(n.remove(),t.removeData(e))},cleanUp:function(t){u.effects.restoreStyle(t),u.effects.removePlaceholder(t)},setTransition:function(i,t,o,s){return s=s||{},u.each(t,function(t,e){var n=i.cssUnit(e);0<n[0]&&(s[e]=n[0]*o+n[1])}),s}}),u.fn.extend({effect:function(){function t(t){var e=u(this),n=u.effects.mode(e,a)||s;e.data(p,!0),c.push(n),s&&("show"===n||n===s&&"hide"===n)&&e.show(),s&&"none"===n||u.effects.saveStyle(e),"function"==typeof t&&t()}var i=g.apply(this,arguments),o=u.effects.effect[i.effect],s=o.mode,e=i.queue,n=e||"fx",r=i.complete,a=i.mode,c=[];return u.fx.off||!o?a?this[a](i.duration,r):this.each(function(){r&&r.call(this)}):!1===e?this.each(t).each(f):this.queue(n,t).queue(n,f);function f(t){var e=u(this);function n(){"function"==typeof r&&r.call(e[0]),"function"==typeof t&&t()}i.mode=c.shift(),!1===u.uiBackCompat||s?"none"===i.mode?(e[a](),n()):o.call(e[0],i,function(){e.removeData(p),u.effects.cleanUp(e),"hide"===i.mode&&e.hide(),n()}):(e.is(":hidden")?"hide"===a:"show"===a)?(e[a](),n()):o.call(e[0],i,n)}},show:(f=u.fn.show,function(t){return v(t)?f.apply(this,arguments):((t=g.apply(this,arguments)).mode="show",this.effect.call(this,t))}),hide:(i=u.fn.hide,function(t){return v(t)?i.apply(this,arguments):((t=g.apply(this,arguments)).mode="hide",this.effect.call(this,t))}),toggle:(n=u.fn.toggle,function(t){return v(t)||"boolean"==typeof t?n.apply(this,arguments):((t=g.apply(this,arguments)).mode="toggle",this.effect.call(this,t))}),cssUnit:function(t){var n=this.css(t),i=[];return u.each(["em","px","%","pt"],function(t,e){0<n.indexOf(e)&&(i=[parseFloat(n),e])}),i},cssClip:function(t){return t?this.css("clip","rect("+t.top+"px "+t.right+"px "+t.bottom+"px "+t.left+"px)"):y(this.css("clip"),this)},transfer:function(t,e){var n=u(this),i=u(t.to),o="fixed"===i.css("position"),s=u("body"),r=o?s.scrollTop():0,s=o?s.scrollLeft():0,a=i.offset(),a={top:a.top-r,left:a.left-s,height:i.innerHeight(),width:i.innerWidth()},i=n.offset(),c=u("<div class='ui-effects-transfer'></div>");c.appendTo("body").addClass(t.className).css({top:i.top-r,left:i.left-s,height:n.innerHeight(),width:n.innerWidth(),position:o?"fixed":"absolute"}).animate(a,t.duration,t.easing,function(){c.remove(),"function"==typeof e&&e()})}}),u.fx.step.clip=function(t){t.clipInit||(t.start=u(t.elem).cssClip(),"string"==typeof t.end&&(t.end=y(t.end,t.elem)),t.clipInit=!0),u(t.elem).cssClip({top:t.pos*(t.end.top-t.start.top)+t.start.top,right:t.pos*(t.end.right-t.start.right)+t.start.right,bottom:t.pos*(t.end.bottom-t.start.bottom)+t.start.bottom,left:t.pos*(t.end.left-t.start.left)+t.start.left})},l={},u.each(["Quad","Cubic","Quart","Quint","Expo"],function(e,t){l[t]=function(t){return Math.pow(t,e+2)}}),u.extend(l,{Sine:function(t){return 1-Math.cos(t*Math.PI/2)},Circ:function(t){return 1-Math.sqrt(1-t*t)},Elastic:function(t){return 0===t||1===t?t:-Math.pow(2,8*(t-1))*Math.sin((80*(t-1)-7.5)*Math.PI/15)},Back:function(t){return t*t*(3*t-2)},Bounce:function(t){for(var e,n=4;t<((e=Math.pow(2,--n))-1)/11;);return 1/Math.pow(4,3-n)-7.5625*Math.pow((3*e-2)/22-t,2)}}),u.each(l,function(t,e){u.easing["easeIn"+t]=e,u.easing["easeOut"+t]=function(t){return 1-e(1-t)},u.easing["easeInOut"+t]=function(t){return t<.5?e(2*t)/2:1-e(-2*t+2)/2}}),u.effects});