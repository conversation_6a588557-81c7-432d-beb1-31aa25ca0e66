tinymce.WPWindowManager=tinymce.InlineWindowManager=function(a){if(this.wp)return this;this.wp={},this.parent=a.windowManager,this.editor=a,tinymce.extend(this,this.parent),this.open=function(e,i){var n,o=this,t=this.wp;if(!e.wpDialog)return this.parent.open.apply(this,arguments);e.id&&("undefined"!=typeof jQuery&&jQuery.wp&&jQuery.wp.wpdialog?(t.$element=n=jQuery("#"+e.id),n.length&&(window.console&&window.console.log&&window.console.log("tinymce.WPWindowManager is deprecated. Use the default editor.windowManager to open dialogs with inline HTML."),t.features=e,t.params=i,a.nodeChanged(),n.data("wpdialog")||n.wpdialog({title:e.title,width:e.width,height:e.height,modal:!0,dialogClass:"wp-dialog",zIndex:3e5}),n.wpdialog("open"),n.on("wpdialogclose",function(){o.wp.$element&&(o.wp={})}))):window.console&&window.console.error&&window.console.error('wpdialog.js is not loaded. Please set "wpdialogs" as dependency for your script when calling wp_enqueue_script(). You may also want to enqueue the "wp-jquery-ui-dialog" stylesheet.'))},this.close=function(){if(!this.wp.features||!this.wp.features.wpDialog)return this.parent.close.apply(this,arguments);this.wp.$element.wpdialog("close")}},tinymce.PluginManager.add("wpdialogs",function(e){e.on("init",function(){e.windowManager=new tinymce.WPWindowManager(e)})});