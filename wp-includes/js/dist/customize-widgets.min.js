/*! This file is auto-generated */
(()=>{"use strict";var e={7734:e=>{e.exports=function e(t,s){if(t===s)return!0;if(t&&s&&"object"==typeof t&&"object"==typeof s){if(t.constructor!==s.constructor)return!1;var i,r,o;if(Array.isArray(t)){if((i=t.length)!=s.length)return!1;for(r=i;0!=r--;)if(!e(t[r],s[r]))return!1;return!0}if(t instanceof Map&&s instanceof Map){if(t.size!==s.size)return!1;for(r of t.entries())if(!s.has(r[0]))return!1;for(r of t.entries())if(!e(r[1],s.get(r[0])))return!1;return!0}if(t instanceof Set&&s instanceof Set){if(t.size!==s.size)return!1;for(r of t.entries())if(!s.has(r[0]))return!1;return!0}if(ArrayBuffer.isView(t)&&ArrayBuffer.isView(s)){if((i=t.length)!=s.length)return!1;for(r=i;0!=r--;)if(t[r]!==s[r])return!1;return!0}if(t.constructor===RegExp)return t.source===s.source&&t.flags===s.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===s.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===s.toString();if((i=(o=Object.keys(t)).length)!==Object.keys(s).length)return!1;for(r=i;0!=r--;)if(!Object.prototype.hasOwnProperty.call(s,o[r]))return!1;for(r=i;0!=r--;){var n=o[r];if(!e(t[n],s[n]))return!1}return!0}return t!=t&&s!=s}}},t={};function s(i){var r=t[i];if(void 0!==r)return r.exports;var o=t[i]={exports:{}};return e[i](o,o.exports,s),o.exports}s.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return s.d(t,{a:t}),t},s.d=(e,t)=>{for(var i in t)s.o(t,i)&&!s.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:t[i]})},s.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),s.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var i={};s.r(i),s.d(i,{initialize:()=>Fe,store:()=>N});var r={};s.r(r),s.d(r,{__experimentalGetInsertionPoint:()=>O,isInserterOpened:()=>M});var o={};s.r(o),s.d(o,{setIsInserterOpened:()=>T});const n=window.wp.element,c=window.wp.blockLibrary,a=window.wp.widgets,d=window.wp.blocks,l=window.wp.data,u=window.wp.preferences,h=window.wp.components,p=window.wp.i18n,m=window.wp.blockEditor,g=window.wp.compose,b=window.wp.hooks,w=window.ReactJSXRuntime;function f({text:e,children:t}){const s=(0,g.useCopyToClipboard)(e);return(0,w.jsx)(h.Button,{size:"compact",variant:"secondary",ref:s,children:t})}class _ extends n.Component{constructor(){super(...arguments),this.state={error:null}}componentDidCatch(e){this.setState({error:e}),(0,b.doAction)("editor.ErrorBoundary.errorLogged",e)}render(){const{error:e}=this.state;return e?(0,w.jsx)(m.Warning,{className:"customize-widgets-error-boundary",actions:[(0,w.jsx)(f,{text:e.stack,children:(0,p.__)("Copy Error")},"copy-error")],children:(0,p.__)("The editor has encountered an unexpected error.")}):this.props.children}}const x=window.wp.coreData,y=window.wp.mediaUtils;const k=function({inspector:e,closeMenu:t,...s}){const i=(0,l.useSelect)((e=>e(m.store).getSelectedBlockClientId()),[]),r=(0,n.useMemo)((()=>document.getElementById(`block-${i}`)),[i]);return(0,w.jsx)(h.MenuItem,{onClick:()=>{e.open({returnFocusWhenClose:r}),t()},...s,children:(0,p.__)("Show more settings")})};function v(e){var t,s,i="";if("string"==typeof e||"number"==typeof e)i+=e;else if("object"==typeof e)if(Array.isArray(e)){var r=e.length;for(t=0;t<r;t++)e[t]&&(s=v(e[t]))&&(i&&(i+=" "),i+=s)}else for(s in e)e[s]&&(i&&(i+=" "),i+=s);return i}const C=function(){for(var e,t,s=0,i="",r=arguments.length;s<r;s++)(e=arguments[s])&&(t=v(e))&&(i&&(i+=" "),i+=t);return i},S=window.wp.keycodes,j=window.wp.primitives,I=(0,w.jsx)(j.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,w.jsx)(j.Path,{d:"M18.3 11.7c-.6-.6-1.4-.9-2.3-.9H6.7l2.9-3.3-1.1-1-4.5 5L8.5 16l1-1-2.7-2.7H16c.5 0 .9.2 1.3.5 1 1 1 3.4 1 4.5v.3h1.5v-.2c0-1.5 0-4.3-1.5-5.7z"})}),z=(0,w.jsx)(j.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,w.jsx)(j.Path,{d:"M15.6 6.5l-1.1 1 2.9 3.3H8c-.9 0-1.7.3-2.3.9-1.4 1.5-1.4 4.2-1.4 5.6v.2h1.5v-.3c0-1.1 0-3.5 1-4.5.3-.3.7-.5 1.3-.5h9.2L14.5 15l1.1 1.1 4.6-4.6-4.6-5z"})}),W=(0,w.jsx)(j.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,w.jsx)(j.Path,{d:"M11 12.5V17.5H12.5V12.5H17.5V11H12.5V6H11V11H6V12.5H11Z"})}),B=(0,w.jsx)(j.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,w.jsx)(j.Path,{d:"M12 13.06l3.712 3.713 1.061-1.06L13.061 12l3.712-3.712-1.06-1.06L12 10.938 8.288 7.227l-1.061 1.06L10.939 12l-3.712 3.712 1.06 1.061L12 13.061z"})});const E=(0,l.combineReducers)({blockInserterPanel:function(e=!1,t){return"SET_IS_INSERTER_OPENED"===t.type?t.value:e}}),A={rootClientId:void 0,insertionIndex:void 0};function M(e){return!!e.blockInserterPanel}function O(e){return"boolean"==typeof e.blockInserterPanel?A:e.blockInserterPanel}function T(e){return{type:"SET_IS_INSERTER_OPENED",value:e}}const P={reducer:E,selectors:r,actions:o},N=(0,l.createReduxStore)("core/customize-widgets",P);(0,l.register)(N);const F=function e({setIsOpened:t}){const s=(0,g.useInstanceId)(e,"customize-widget-layout__inserter-panel-title"),i=(0,l.useSelect)((e=>e(N).__experimentalGetInsertionPoint()),[]);return(0,w.jsxs)("div",{className:"customize-widgets-layout__inserter-panel","aria-labelledby":s,children:[(0,w.jsxs)("div",{className:"customize-widgets-layout__inserter-panel-header",children:[(0,w.jsx)("h2",{id:s,className:"customize-widgets-layout__inserter-panel-header-title",children:(0,p.__)("Add a block")}),(0,w.jsx)(h.Button,{size:"small",icon:B,onClick:()=>t(!1),"aria-label":(0,p.__)("Close inserter")})]}),(0,w.jsx)("div",{className:"customize-widgets-layout__inserter-panel-content",children:(0,w.jsx)(m.__experimentalLibrary,{rootClientId:i.rootClientId,__experimentalInsertionIndex:i.insertionIndex,showInserterHelpPanel:!0,onSelect:()=>t(!1)})})]})},L=(0,w.jsx)(j.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,w.jsx)(j.Path,{d:"M13 19h-2v-2h2v2zm0-6h-2v-2h2v2zm0-6h-2V5h2v2z"})}),D=(0,w.jsx)(j.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,w.jsx)(j.Path,{d:"M19.5 4.5h-7V6h4.44l-5.97 5.97 1.06 1.06L18 7.06v4.44h1.5v-7Zm-13 1a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-3H17v3a.5.5 0 0 1-.5.5h-10a.5.5 0 0 1-.5-.5v-10a.5.5 0 0 1 .5-.5h3V5.5h-3Z"})}),H=window.wp.keyboardShortcuts,R=[{keyCombination:{modifier:"primary",character:"b"},description:(0,p.__)("Make the selected text bold.")},{keyCombination:{modifier:"primary",character:"i"},description:(0,p.__)("Make the selected text italic.")},{keyCombination:{modifier:"primary",character:"k"},description:(0,p.__)("Convert the selected text into a link.")},{keyCombination:{modifier:"primaryShift",character:"k"},description:(0,p.__)("Remove a link.")},{keyCombination:{character:"[["},description:(0,p.__)("Insert a link to a post or page.")},{keyCombination:{modifier:"primary",character:"u"},description:(0,p.__)("Underline the selected text.")},{keyCombination:{modifier:"access",character:"d"},description:(0,p.__)("Strikethrough the selected text.")},{keyCombination:{modifier:"access",character:"x"},description:(0,p.__)("Make the selected text inline code.")},{keyCombination:{modifier:"access",character:"0"},aliases:[{modifier:"access",character:"7"}],description:(0,p.__)("Convert the current heading to a paragraph.")},{keyCombination:{modifier:"access",character:"1-6"},description:(0,p.__)("Convert the current paragraph or heading to a heading of level 1 to 6.")},{keyCombination:{modifier:"primaryShift",character:"SPACE"},description:(0,p.__)("Add non breaking space.")}];function G({keyCombination:e,forceAriaLabel:t}){const s=e.modifier?S.displayShortcutList[e.modifier](e.character):e.character,i=e.modifier?S.shortcutAriaLabel[e.modifier](e.character):e.character;return(0,w.jsx)("kbd",{className:"customize-widgets-keyboard-shortcut-help-modal__shortcut-key-combination","aria-label":t||i,children:(Array.isArray(s)?s:[s]).map(((e,t)=>"+"===e?(0,w.jsx)(n.Fragment,{children:e},t):(0,w.jsx)("kbd",{className:"customize-widgets-keyboard-shortcut-help-modal__shortcut-key",children:e},t)))})}const V=function({description:e,keyCombination:t,aliases:s=[],ariaLabel:i}){return(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)("div",{className:"customize-widgets-keyboard-shortcut-help-modal__shortcut-description",children:e}),(0,w.jsxs)("div",{className:"customize-widgets-keyboard-shortcut-help-modal__shortcut-term",children:[(0,w.jsx)(G,{keyCombination:t,forceAriaLabel:i}),s.map(((e,t)=>(0,w.jsx)(G,{keyCombination:e,forceAriaLabel:i},t)))]})]})};const U=function({name:e}){const{keyCombination:t,description:s,aliases:i}=(0,l.useSelect)((t=>{const{getShortcutKeyCombination:s,getShortcutDescription:i,getShortcutAliases:r}=t(H.store);return{keyCombination:s(e),aliases:r(e),description:i(e)}}),[e]);return t?(0,w.jsx)(V,{keyCombination:t,description:s,aliases:i}):null},$=({shortcuts:e})=>(0,w.jsx)("ul",{className:"customize-widgets-keyboard-shortcut-help-modal__shortcut-list",role:"list",children:e.map(((e,t)=>(0,w.jsx)("li",{className:"customize-widgets-keyboard-shortcut-help-modal__shortcut",children:"string"==typeof e?(0,w.jsx)(U,{name:e}):(0,w.jsx)(V,{...e})},t)))}),q=({title:e,shortcuts:t,className:s})=>(0,w.jsxs)("section",{className:C("customize-widgets-keyboard-shortcut-help-modal__section",s),children:[!!e&&(0,w.jsx)("h2",{className:"customize-widgets-keyboard-shortcut-help-modal__section-title",children:e}),(0,w.jsx)($,{shortcuts:t})]}),K=({title:e,categoryName:t,additionalShortcuts:s=[]})=>{const i=(0,l.useSelect)((e=>e(H.store).getCategoryShortcuts(t)),[t]);return(0,w.jsx)(q,{title:e,shortcuts:i.concat(s)})};function Z({isModalActive:e,toggleModal:t}){const{registerShortcut:s}=(0,l.useDispatch)(H.store);return s({name:"core/customize-widgets/keyboard-shortcuts",category:"main",description:(0,p.__)("Display these keyboard shortcuts."),keyCombination:{modifier:"access",character:"h"}}),(0,H.useShortcut)("core/customize-widgets/keyboard-shortcuts",t),e?(0,w.jsxs)(h.Modal,{className:"customize-widgets-keyboard-shortcut-help-modal",title:(0,p.__)("Keyboard shortcuts"),onRequestClose:t,children:[(0,w.jsx)(q,{className:"customize-widgets-keyboard-shortcut-help-modal__main-shortcuts",shortcuts:["core/customize-widgets/keyboard-shortcuts"]}),(0,w.jsx)(K,{title:(0,p.__)("Global shortcuts"),categoryName:"global"}),(0,w.jsx)(K,{title:(0,p.__)("Selection shortcuts"),categoryName:"selection"}),(0,w.jsx)(K,{title:(0,p.__)("Block shortcuts"),categoryName:"block",additionalShortcuts:[{keyCombination:{character:"/"},description:(0,p.__)("Change the block type after adding a new paragraph."),ariaLabel:(0,p.__)("Forward-slash")}]}),(0,w.jsx)(q,{title:(0,p.__)("Text formatting"),shortcuts:R})]}):null}function Y(){const[e,t]=(0,n.useState)(!1),s=()=>t(!e);return(0,H.useShortcut)("core/customize-widgets/keyboard-shortcuts",s),(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)(h.ToolbarDropdownMenu,{icon:L,label:(0,p.__)("Options"),popoverProps:{placement:"bottom-end",className:"more-menu-dropdown__content"},toggleProps:{tooltipPosition:"bottom",size:"compact"},children:()=>(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)(h.MenuGroup,{label:(0,p._x)("View","noun"),children:(0,w.jsx)(u.PreferenceToggleMenuItem,{scope:"core/customize-widgets",name:"fixedToolbar",label:(0,p.__)("Top toolbar"),info:(0,p.__)("Access all block and document tools in a single place"),messageActivated:(0,p.__)("Top toolbar activated"),messageDeactivated:(0,p.__)("Top toolbar deactivated")})}),(0,w.jsxs)(h.MenuGroup,{label:(0,p.__)("Tools"),children:[(0,w.jsx)(h.MenuItem,{onClick:()=>{t(!0)},shortcut:S.displayShortcut.access("h"),children:(0,p.__)("Keyboard shortcuts")}),(0,w.jsx)(u.PreferenceToggleMenuItem,{scope:"core/customize-widgets",name:"welcomeGuide",label:(0,p.__)("Welcome Guide")}),(0,w.jsxs)(h.MenuItem,{role:"menuitem",icon:D,href:(0,p.__)("https://wordpress.org/documentation/article/block-based-widgets-editor/"),target:"_blank",rel:"noopener noreferrer",children:[(0,p.__)("Help"),(0,w.jsx)(h.VisuallyHidden,{as:"span",children:(0,p.__)("(opens in a new tab)")})]})]}),(0,w.jsx)(h.MenuGroup,{label:(0,p.__)("Preferences"),children:(0,w.jsx)(u.PreferenceToggleMenuItem,{scope:"core/customize-widgets",name:"keepCaretInsideBlock",label:(0,p.__)("Contain text cursor inside block"),info:(0,p.__)("Aids screen readers by stopping text caret from leaving blocks."),messageActivated:(0,p.__)("Contain text cursor inside block activated"),messageDeactivated:(0,p.__)("Contain text cursor inside block deactivated")})})]})}),(0,w.jsx)(Z,{isModalActive:e,toggleModal:s})]})}const J=function({sidebar:e,inserter:t,isInserterOpened:s,setIsInserterOpened:i,isFixedToolbarActive:r}){const[[o,c],a]=(0,n.useState)([e.hasUndo(),e.hasRedo()]),d=(0,S.isAppleOS)()?S.displayShortcut.primaryShift("z"):S.displayShortcut.primary("y");return(0,n.useEffect)((()=>e.subscribeHistory((()=>{a([e.hasUndo(),e.hasRedo()])}))),[e]),(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)("div",{className:C("customize-widgets-header",{"is-fixed-toolbar-active":r}),children:(0,w.jsxs)(m.NavigableToolbar,{className:"customize-widgets-header-toolbar","aria-label":(0,p.__)("Document tools"),children:[(0,w.jsx)(h.ToolbarButton,{icon:(0,p.isRTL)()?z:I,label:(0,p.__)("Undo"),shortcut:S.displayShortcut.primary("z"),disabled:!o,onClick:e.undo,className:"customize-widgets-editor-history-button undo-button"}),(0,w.jsx)(h.ToolbarButton,{icon:(0,p.isRTL)()?I:z,label:(0,p.__)("Redo"),shortcut:d,disabled:!c,onClick:e.redo,className:"customize-widgets-editor-history-button redo-button"}),(0,w.jsx)(h.ToolbarButton,{className:"customize-widgets-header-toolbar__inserter-toggle",isPressed:s,variant:"primary",icon:W,label:(0,p._x)("Add block","Generic label for block inserter button"),onClick:()=>{i((e=>!e))}}),(0,w.jsx)(Y,{})]})}),(0,n.createPortal)((0,w.jsx)(F,{setIsOpened:i}),t.contentContainer[0])]})};var X=s(7734),Q=s.n(X);const ee=window.wp.isShallowEqual;var te=s.n(ee);function se(e){const t=e.match(/^widget_(.+)(?:\[(\d+)\])$/);if(t){return`${t[1]}-${parseInt(t[2],10)}`}return e}function ie(e,t=null){let s;if("core/legacy-widget"===e.name&&(e.attributes.id||e.attributes.instance))if(e.attributes.id)s={id:e.attributes.id};else{const{encoded:i,hash:r,raw:o,...n}=e.attributes.instance;s={idBase:e.attributes.idBase,instance:{...t?.instance,is_widget_customizer_js_value:!0,encoded_serialized_instance:i,instance_hash_key:r,raw_instance:o,...n}}}else{s={idBase:"block",widgetClass:"WP_Widget_Block",instance:{raw_instance:{content:(0,d.serialize)(e)}}}}const{form:i,rendered:r,...o}=t||{};return{...o,...s}}function re({id:e,idBase:t,number:s,instance:i}){let r;const{encoded_serialized_instance:o,instance_hash_key:n,raw_instance:c,...l}=i;if("block"===t){var u;const e=(0,d.parse)(null!==(u=c.content)&&void 0!==u?u:"",{__unstableSkipAutop:!0});r=e.length?e[0]:(0,d.createBlock)("core/paragraph",{})}else r=s?(0,d.createBlock)("core/legacy-widget",{idBase:t,instance:{encoded:o,hash:n,raw:c,...l}}):(0,d.createBlock)("core/legacy-widget",{id:e});return(0,a.addWidgetIdToBlock)(r,e)}function oe(e){const[t,s]=(0,n.useState)((()=>e.getWidgets().map((e=>re(e)))));(0,n.useEffect)((()=>e.subscribe(((e,t)=>{s((s=>{const i=new Map(e.map((e=>[e.id,e]))),r=new Map(s.map((e=>[(0,a.getWidgetIdFromBlock)(e),e]))),o=t.map((e=>{const t=i.get(e.id);return t&&t===e?r.get(e.id):re(e)}));return te()(s,o)?s:o}))}))),[e]);const i=(0,n.useCallback)((t=>{s((s=>{if(te()(s,t))return s;const i=new Map(s.map((e=>[(0,a.getWidgetIdFromBlock)(e),e]))),r=t.map((t=>{const s=(0,a.getWidgetIdFromBlock)(t);if(s&&i.has(s)){const r=i.get(s),o=e.getWidget(s);return Q()(t,r)&&o?o:ie(t,o)}return ie(t)}));if(te()(e.getWidgets(),r))return s;const o=e.setWidgets(r);return t.reduce(((e,s,i)=>{const r=o[i];return null!==r&&(e===t&&(e=t.slice()),e[i]=(0,a.addWidgetIdToBlock)(s,r)),e}),t)}))}),[e]);return[t,i,i]}const ne=(0,n.createContext)();function ce({api:e,sidebarControls:t,children:s}){const[i,r]=(0,n.useState)({current:null}),o=(0,n.useCallback)((e=>{for(const s of t){if(s.setting.get().includes(e)){s.sectionInstance.expand({completeCallback(){r({current:e})}});break}}}),[t]);(0,n.useEffect)((()=>{function t(e){const t=se(e);o(t)}let s=!1;function i(){e.previewer.preview.bind("focus-control-for-setting",t),s=!0}return e.previewer.bind("ready",i),()=>{e.previewer.unbind("ready",i),s&&e.previewer.preview.unbind("focus-control-for-setting",t)}}),[e,o]);const c=(0,n.useMemo)((()=>[i,o]),[i,o]);return(0,w.jsx)(ne.Provider,{value:c,children:s})}const ae=()=>(0,n.useContext)(ne);const de=window.wp.privateApis,{lock:le,unlock:ue}=(0,de.__dangerousOptInToUnstableAPIsOnlyForCoreModules)("I acknowledge private features are not for use in themes or plugins and doing so will break in the next version of WordPress.","@wordpress/customize-widgets"),{ExperimentalBlockEditorProvider:he}=ue(m.privateApis);function pe({sidebar:e,settings:t,children:s}){const[i,r,o]=oe(e);return function(e){const{selectBlock:t}=(0,l.useDispatch)(m.store),[s]=ae(),i=(0,n.useRef)(e);(0,n.useEffect)((()=>{i.current=e}),[e]),(0,n.useEffect)((()=>{if(s.current){const e=i.current.find((e=>(0,a.getWidgetIdFromBlock)(e)===s.current));if(e){t(e.clientId);const s=document.querySelector(`[data-block="${e.clientId}"]`);s?.focus()}}}),[s,t])}(i),(0,w.jsx)(he,{value:i,onInput:r,onChange:o,settings:t,useSubRegistry:!1,children:s})}function me({sidebar:e}){const{toggle:t}=(0,l.useDispatch)(u.store),s=e.getWidgets().every((e=>e.id.startsWith("block-")));return(0,w.jsxs)("div",{className:"customize-widgets-welcome-guide",children:[(0,w.jsx)("div",{className:"customize-widgets-welcome-guide__image__wrapper",children:(0,w.jsxs)("picture",{children:[(0,w.jsx)("source",{srcSet:"https://s.w.org/images/block-editor/welcome-editor.svg",media:"(prefers-reduced-motion: reduce)"}),(0,w.jsx)("img",{className:"customize-widgets-welcome-guide__image",src:"https://s.w.org/images/block-editor/welcome-editor.gif",width:"312",height:"240",alt:""})]})}),(0,w.jsx)("h1",{className:"customize-widgets-welcome-guide__heading",children:(0,p.__)("Welcome to block Widgets")}),(0,w.jsx)("p",{className:"customize-widgets-welcome-guide__text",children:s?(0,p.__)("Your theme provides different “block” areas for you to add and edit content. Try adding a search bar, social icons, or other types of blocks here and see how they’ll look on your site."):(0,p.__)("You can now add any block to your site’s widget areas. Don’t worry, all of your favorite widgets still work flawlessly.")}),(0,w.jsx)(h.Button,{size:"compact",variant:"primary",onClick:()=>t("core/customize-widgets","welcomeGuide"),children:(0,p.__)("Got it")}),(0,w.jsx)("hr",{className:"customize-widgets-welcome-guide__separator"}),!s&&(0,w.jsxs)("p",{className:"customize-widgets-welcome-guide__more-info",children:[(0,p.__)("Want to stick with the old widgets?"),(0,w.jsx)("br",{}),(0,w.jsx)(h.ExternalLink,{href:(0,p.__)("https://wordpress.org/plugins/classic-widgets/"),children:(0,p.__)("Get the Classic Widgets plugin.")})]}),(0,w.jsxs)("p",{className:"customize-widgets-welcome-guide__more-info",children:[(0,p.__)("New to the block editor?"),(0,w.jsx)("br",{}),(0,w.jsx)(h.ExternalLink,{href:(0,p.__)("https://wordpress.org/documentation/article/wordpress-block-editor/"),children:(0,p.__)("Here's a detailed guide.")})]})]})}function ge({undo:e,redo:t,save:s}){return(0,H.useShortcut)("core/customize-widgets/undo",(t=>{e(),t.preventDefault()})),(0,H.useShortcut)("core/customize-widgets/redo",(e=>{t(),e.preventDefault()})),(0,H.useShortcut)("core/customize-widgets/save",(e=>{e.preventDefault(),s()})),null}ge.Register=function(){const{registerShortcut:e,unregisterShortcut:t}=(0,l.useDispatch)(H.store);return(0,n.useEffect)((()=>(e({name:"core/customize-widgets/undo",category:"global",description:(0,p.__)("Undo your last changes."),keyCombination:{modifier:"primary",character:"z"}}),e({name:"core/customize-widgets/redo",category:"global",description:(0,p.__)("Redo your last undo."),keyCombination:{modifier:"primaryShift",character:"z"},aliases:(0,S.isAppleOS)()?[]:[{modifier:"primary",character:"y"}]}),e({name:"core/customize-widgets/save",category:"global",description:(0,p.__)("Save your changes."),keyCombination:{modifier:"primary",character:"s"}}),()=>{t("core/customize-widgets/undo"),t("core/customize-widgets/redo"),t("core/customize-widgets/save")})),[e]),null};const be=ge;function we(e){const t=(0,n.useRef)(),s=(0,l.useSelect)((e=>0===e(m.store).getBlockCount()));return(0,n.useEffect)((()=>{if(s&&t.current){const{ownerDocument:e}=t.current;e.activeElement&&e.activeElement!==e.body||t.current.focus()}}),[s]),(0,w.jsx)(m.ButtonBlockAppender,{...e,ref:t})}const{ExperimentalBlockCanvas:fe}=ue(m.privateApis),{BlockKeyboardShortcuts:_e}=ue(c.privateApis);function xe({blockEditorSettings:e,sidebar:t,inserter:s,inspector:i}){const[r,o]=function(e){const t=(0,l.useSelect)((e=>e(N).isInserterOpened()),[]),{setIsInserterOpened:s}=(0,l.useDispatch)(N);return(0,n.useEffect)((()=>{t?e.open():e.close()}),[e,t]),[t,(0,n.useCallback)((e=>{let t=e;"function"==typeof e&&(t=e((0,l.select)(N).isInserterOpened())),s(t)}),[s])]}(s),c=(0,g.useViewportMatch)("small"),{hasUploadPermissions:a,isFixedToolbarActive:d,keepCaretInsideBlock:h,isWelcomeGuideActive:p}=(0,l.useSelect)((e=>{var t;const{get:s}=e(u.store);return{hasUploadPermissions:null===(t=e(x.store).canUser("create",{kind:"root",name:"media"}))||void 0===t||t,isFixedToolbarActive:!!s("core/customize-widgets","fixedToolbar"),keepCaretInsideBlock:!!s("core/customize-widgets","keepCaretInsideBlock"),isWelcomeGuideActive:!!s("core/customize-widgets","welcomeGuide")}}),[]),b=(0,n.useMemo)((()=>{let t;return a&&(t=({onError:t,...s})=>{(0,y.uploadMedia)({wpAllowedMimeTypes:e.allowedMimeTypes,onError:({message:e})=>t(e),...s})}),{...e,__experimentalSetIsInserterOpened:o,mediaUpload:t,hasFixedToolbar:d||!c,keepCaretInsideBlock:h,editorTool:"edit",__unstableHasCustomAppender:!0}}),[a,e,d,c,h,o]);return p?(0,w.jsx)(me,{sidebar:t}):(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)(be.Register,{}),(0,w.jsx)(_e,{}),(0,w.jsxs)(pe,{sidebar:t,settings:b,children:[(0,w.jsx)(be,{undo:t.undo,redo:t.redo,save:t.save}),(0,w.jsx)(J,{sidebar:t,inserter:s,isInserterOpened:r,setIsInserterOpened:o,isFixedToolbarActive:d||!c}),(d||!c)&&(0,w.jsx)(m.BlockToolbar,{hideDragHandle:!0}),(0,w.jsx)(fe,{shouldIframe:!1,styles:b.defaultEditorStyles,height:"100%",children:(0,w.jsx)(m.BlockList,{renderAppender:we})}),(0,n.createPortal)((0,w.jsx)("form",{onSubmit:e=>e.preventDefault(),children:(0,w.jsx)(m.BlockInspector,{})}),i.contentContainer[0])]}),(0,w.jsx)(m.__unstableBlockSettingsMenuFirstItem,{children:({onClose:e})=>(0,w.jsx)(k,{inspector:i,closeMenu:e})})]})}const ye=(0,n.createContext)();function ke({sidebarControls:e,activeSidebarControl:t,children:s}){const i=(0,n.useMemo)((()=>({sidebarControls:e,activeSidebarControl:t})),[e,t]);return(0,w.jsx)(ye.Provider,{value:i,children:s})}function ve({api:e,sidebarControls:t,blockEditorSettings:s}){const[i,r]=(0,n.useState)(null),o=document.getElementById("customize-theme-controls"),c=(0,n.useRef)();!function(e,t){const{hasSelectedBlock:s,hasMultiSelection:i}=(0,l.useSelect)(m.store),{clearSelectedBlock:r}=(0,l.useDispatch)(m.store);(0,n.useEffect)((()=>{if(t.current&&e){const o=e.inspector,n=e.container[0],c=n.ownerDocument,a=c.defaultView;function d(e){!s()&&!i()||!e||!c.contains(e)||n.contains(e)||t.current.contains(e)||e.closest('[role="dialog"]')||o.expanded()||r()}function l(e){d(e.target)}function u(){d(c.activeElement)}return c.addEventListener("mousedown",l),a.addEventListener("blur",u),()=>{c.removeEventListener("mousedown",l),a.removeEventListener("blur",u)}}}),[t,e,s,i,r])}(i,c),(0,n.useEffect)((()=>{const e=t.map((e=>e.subscribe((t=>{t&&r(e)}))));return()=>{e.forEach((e=>e()))}}),[t]);const a=i&&(0,n.createPortal)((0,w.jsx)(_,{children:(0,w.jsx)(xe,{blockEditorSettings:s,sidebar:i.sidebarAdapter,inserter:i.inserter,inspector:i.inspector},i.id)}),i.container[0]),d=o&&(0,n.createPortal)((0,w.jsx)("div",{className:"customize-widgets-popover",ref:c,children:(0,w.jsx)(h.Popover.Slot,{})}),o);return(0,w.jsx)(h.SlotFillProvider,{children:(0,w.jsx)(ke,{sidebarControls:t,activeSidebarControl:i,children:(0,w.jsxs)(ce,{api:e,sidebarControls:t,children:[a,d]})})})}const Ce=e=>`widgets-inspector-${e}`;function Se(){const{wp:{customize:e}}=window,t=window.matchMedia("(prefers-reduced-motion: reduce)");let s=t.matches;return t.addEventListener("change",(e=>{s=e.matches})),class extends e.Section{ready(){const t=function(){const{wp:{customize:e}}=window;return class extends e.Section{constructor(e,t){super(e,t),this.parentSection=t.parentSection,this.returnFocusWhenClose=null,this._isOpen=!1}get isOpen(){return this._isOpen}set isOpen(e){this._isOpen=e,this.triggerActiveCallbacks()}ready(){this.contentContainer[0].classList.add("customize-widgets-layout__inspector")}isContextuallyActive(){return this.isOpen}onChangeExpanded(e,t){super.onChangeExpanded(e,t),this.parentSection&&!t.unchanged&&(e?this.parentSection.collapse({manualTransition:!0}):this.parentSection.expand({manualTransition:!0,completeCallback:()=>{this.returnFocusWhenClose&&!this.contentContainer[0].contains(this.returnFocusWhenClose)&&this.returnFocusWhenClose.focus()}}))}open({returnFocusWhenClose:e}={}){this.isOpen=!0,this.returnFocusWhenClose=e,this.expand({allowMultiple:!0})}close(){this.collapse({allowMultiple:!0})}collapse(e){this.isOpen=!1,super.collapse(e)}triggerActiveCallbacks(){this.active.callbacks.fireWith(this.active,[!1,!0])}}}();this.inspector=new t(Ce(this.id),{title:(0,p.__)("Block Settings"),parentSection:this,customizeAction:[(0,p.__)("Customizing"),(0,p.__)("Widgets"),this.params.title].join(" ▸ ")}),e.section.add(this.inspector),this.contentContainer[0].classList.add("customize-widgets__sidebar-section")}hasSubSectionOpened(){return this.inspector.expanded()}onChangeExpanded(e,t){const i=this.controls(),r={...t,completeCallback(){i.forEach((t=>{t.onChangeSectionExpanded?.(e,r)})),t.completeCallback?.()}};if(r.manualTransition){e?(this.contentContainer.addClass(["busy","open"]),this.contentContainer.removeClass("is-sub-section-open"),this.contentContainer.closest(".wp-full-overlay").addClass("section-open")):(this.contentContainer.addClass(["busy","is-sub-section-open"]),this.contentContainer.closest(".wp-full-overlay").addClass("section-open"),this.contentContainer.removeClass("open"));const t=()=>{this.contentContainer.removeClass("busy"),r.completeCallback()};s?t():this.contentContainer.one("transitionend",t)}else super.onChangeExpanded(e,r)}}}const{wp:je}=window;function Ie(e){const t=e.match(/^(.+)-(\d+)$/);return t?{idBase:t[1],number:parseInt(t[2],10)}:{idBase:e}}function ze(e){const{idBase:t,number:s}=Ie(e);return s?`widget_${t}[${s}]`:`widget_${t}`}class We{constructor(e,t){this.setting=e,this.api=t,this.locked=!1,this.widgetsCache=new WeakMap,this.subscribers=new Set,this.history=[this._getWidgetIds().map((e=>this.getWidget(e)))],this.historyIndex=0,this.historySubscribers=new Set,this._debounceSetHistory=function(e,t,s){let i,r=!1;function o(...o){const n=(r?t:e).apply(this,o);return r=!0,clearTimeout(i),i=setTimeout((()=>{r=!1}),s),n}return o.cancel=()=>{r=!1,clearTimeout(i)},o}(this._pushHistory,this._replaceHistory,1e3),this.setting.bind(this._handleSettingChange.bind(this)),this.api.bind("change",this._handleAllSettingsChange.bind(this)),this.undo=this.undo.bind(this),this.redo=this.redo.bind(this),this.save=this.save.bind(this)}subscribe(e){return this.subscribers.add(e),()=>{this.subscribers.delete(e)}}getWidgets(){return this.history[this.historyIndex]}_emit(...e){for(const t of this.subscribers)t(...e)}_getWidgetIds(){return this.setting.get()}_pushHistory(){this.history=[...this.history.slice(0,this.historyIndex+1),this._getWidgetIds().map((e=>this.getWidget(e)))],this.historyIndex+=1,this.historySubscribers.forEach((e=>e()))}_replaceHistory(){this.history[this.historyIndex]=this._getWidgetIds().map((e=>this.getWidget(e)))}_handleSettingChange(){if(this.locked)return;const e=this.getWidgets();this._pushHistory(),this._emit(e,this.getWidgets())}_handleAllSettingsChange(e){if(this.locked)return;if(!e.id.startsWith("widget_"))return;const t=se(e.id);if(!this.setting.get().includes(t))return;const s=this.getWidgets();this._pushHistory(),this._emit(s,this.getWidgets())}_createWidget(e){const t=je.customize.Widgets.availableWidgets.findWhere({id_base:e.idBase});let s=e.number;t.get("is_multi")&&!s&&(t.set("multi_number",t.get("multi_number")+1),s=t.get("multi_number"));const i=s?`widget_${e.idBase}[${s}]`:`widget_${e.idBase}`,r={transport:je.customize.Widgets.data.selectiveRefreshableWidgets[t.get("id_base")]?"postMessage":"refresh",previewer:this.setting.previewer};this.api.create(i,i,"",r).set(e.instance);return se(i)}_removeWidget(e){const t=ze(e.id),s=this.api(t);if(s){const e=s.get();this.widgetsCache.delete(e)}this.api.remove(t)}_updateWidget(e){const t=this.getWidget(e.id);if(t===e)return e.id;if(t.idBase&&e.idBase&&t.idBase===e.idBase){const t=ze(e.id);return this.api(t).set(e.instance),e.id}return this._removeWidget(e),this._createWidget(e)}getWidget(e){if(!e)return null;const{idBase:t,number:s}=Ie(e),i=ze(e),r=this.api(i);if(!r)return null;const o=r.get();if(this.widgetsCache.has(o))return this.widgetsCache.get(o);const n={id:e,idBase:t,number:s,instance:o};return this.widgetsCache.set(o,n),n}_updateWidgets(e){this.locked=!0;const t=[],s=e.map((e=>{if(e.id&&this.getWidget(e.id))return t.push(null),this._updateWidget(e);const s=this._createWidget(e);return t.push(s),s}));return this.getWidgets().filter((e=>!s.includes(e.id))).forEach((e=>this._removeWidget(e))),this.setting.set(s),this.locked=!1,t}setWidgets(e){const t=this._updateWidgets(e);return this._debounceSetHistory(),t}hasUndo(){return this.historyIndex>0}hasRedo(){return this.historyIndex<this.history.length-1}_seek(e){const t=this.getWidgets();this.historyIndex=e;const s=this.history[this.historyIndex];this._updateWidgets(s),this._emit(t,this.getWidgets()),this.historySubscribers.forEach((e=>e())),this._debounceSetHistory.cancel()}undo(){this.hasUndo()&&this._seek(this.historyIndex-1)}redo(){this.hasRedo()&&this._seek(this.historyIndex+1)}subscribeHistory(e){return this.historySubscribers.add(e),()=>{this.historySubscribers.delete(e)}}save(){this.api.previewer.save()}}const Be=window.wp.dom;const Ee=e=>`widgets-inserter-${e}`;function Ae(){const{wp:{customize:e}}=window;return class extends e.Control{constructor(...e){super(...e),this.subscribers=new Set}ready(){const t=function(){const{wp:{customize:e}}=window,t=e.OuterSection;return e.OuterSection=class extends t{onChangeExpanded(t,s){return t&&e.section.each((e=>{"outer"===e.params.type&&e.id!==this.id&&e.expanded()&&e.collapse()})),super.onChangeExpanded(t,s)}},e.sectionConstructor.outer=e.OuterSection,class extends e.OuterSection{constructor(...e){super(...e),this.params.type="outer",this.activeElementBeforeExpanded=null,this.contentContainer[0].ownerDocument.defaultView.addEventListener("keydown",(e=>{!this.expanded()||e.keyCode!==S.ESCAPE&&"Escape"!==e.code||e.defaultPrevented||(e.preventDefault(),e.stopPropagation(),(0,l.dispatch)(N).setIsInserterOpened(!1))}),!0),this.contentContainer.addClass("widgets-inserter"),this.isFromInternalAction=!1,this.expanded.bind((()=>{this.isFromInternalAction||(0,l.dispatch)(N).setIsInserterOpened(this.expanded()),this.isFromInternalAction=!1}))}open(){if(!this.expanded()){const e=this.contentContainer[0];this.activeElementBeforeExpanded=e.ownerDocument.activeElement,this.isFromInternalAction=!0,this.expand({completeCallback(){const t=Be.focus.tabbable.find(e)[1];t&&t.focus()}})}}close(){if(this.expanded()){const e=this.contentContainer[0],t=e.ownerDocument.activeElement;this.isFromInternalAction=!0,this.collapse({completeCallback(){e.contains(t)&&this.activeElementBeforeExpanded&&this.activeElementBeforeExpanded.focus()}})}}}}();this.inserter=new t(Ee(this.id),{}),e.section.add(this.inserter),this.sectionInstance=e.section(this.section()),this.inspector=this.sectionInstance.inspector,this.sidebarAdapter=new We(this.setting,e)}subscribe(e){return this.subscribers.add(e),()=>{this.subscribers.delete(e)}}onChangeSectionExpanded(e,t){t.unchanged||(e||(0,l.dispatch)(N).setIsInserterOpened(!1),this.subscribers.forEach((s=>s(e,t))))}}}const Me=(0,g.createHigherOrderComponent)((e=>t=>{let s=(0,a.getWidgetIdFromBlock)(t);const i=function(){const{sidebarControls:e}=(0,n.useContext)(ye);return e}(),r=function(){const{activeSidebarControl:e}=(0,n.useContext)(ye);return e}(),o=i?.length>1,c=t.name,d=t.clientId,u=(0,l.useSelect)((e=>e(m.store).canInsertBlockType(c,"")),[c]),h=(0,l.useSelect)((e=>e(m.store).getBlock(d)),[d]),{removeBlock:p}=(0,l.useDispatch)(m.store),[,g]=ae();return(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)(e,{...t},"edit"),o&&u&&(0,w.jsx)(m.BlockControls,{children:(0,w.jsx)(a.MoveToWidgetArea,{widgetAreas:i.map((e=>({id:e.id,name:e.params.label,description:e.params.description}))),currentWidgetAreaId:r?.id,onSelect:function(e){const t=i.find((t=>t.id===e));if(s){const e=r.setting,i=t.setting;e(e().filter((e=>e!==s))),i([...i(),s])}else{const e=t.sidebarAdapter;p(d);const i=e.setWidgets([...e.getWidgets(),ie(h)]);s=i.reverse().find((e=>!!e))}g(s)}})})]})}),"withMoveToSidebarToolbarItem");(0,b.addFilter)("editor.BlockEdit","core/customize-widgets/block-edit",Me);(0,b.addFilter)("editor.MediaUpload","core/edit-widgets/replace-media-upload",(()=>y.MediaUpload));const{wp:Oe}=window,Te=(0,g.createHigherOrderComponent)((e=>t=>{var s;const{idBase:i}=t.attributes,r=null!==(s=Oe.customize.Widgets.data.availableWidgets.find((e=>e.id_base===i))?.is_wide)&&void 0!==s&&s;return(0,w.jsx)(e,{...t,isWide:r},"edit")}),"withWideWidgetDisplay");(0,b.addFilter)("editor.BlockEdit","core/customize-widgets/wide-widget-display",Te);const{wp:Pe}=window,Ne=["core/more","core/block","core/freeform","core/template-part"];function Fe(e,t){(0,l.dispatch)(u.store).setDefaults("core/customize-widgets",{fixedToolbar:!1,welcomeGuide:!0}),(0,l.dispatch)(d.store).reapplyBlockTypeFilters();const s=(0,c.__experimentalGetCoreBlocks)().filter((e=>!(Ne.includes(e.name)||e.name.startsWith("core/post")||e.name.startsWith("core/query")||e.name.startsWith("core/site")||e.name.startsWith("core/navigation"))));(0,c.registerCoreBlocks)(s),(0,a.registerLegacyWidgetBlock)(),(0,a.registerLegacyWidgetVariations)(t),(0,a.registerWidgetGroupBlock)(),(0,d.setFreeformContentHandlerName)("core/html");const i=Ae();Pe.customize.sectionConstructor.sidebar=Se(),Pe.customize.controlConstructor.sidebar_block_editor=i;const r=document.createElement("div");document.body.appendChild(r),Pe.customize.bind("ready",(()=>{const e=[];Pe.customize.control.each((t=>{t instanceof i&&e.push(t)})),(0,n.createRoot)(r).render((0,w.jsx)(n.StrictMode,{children:(0,w.jsx)(ve,{api:Pe.customize,sidebarControls:e,blockEditorSettings:t})}))}))}(window.wp=window.wp||{}).customizeWidgets=i})();