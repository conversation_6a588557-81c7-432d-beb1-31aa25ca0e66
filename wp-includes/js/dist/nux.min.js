/*! This file is auto-generated */
(()=>{"use strict";var e={n:i=>{var n=i&&i.__esModule?()=>i.default:()=>i;return e.d(n,{a:n}),n},d:(i,n)=>{for(var t in n)e.o(n,t)&&!e.o(i,t)&&Object.defineProperty(i,t,{enumerable:!0,get:n[t]})},o:(e,i)=>Object.prototype.hasOwnProperty.call(e,i),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},i={};e.r(i),e.d(i,{DotTip:()=>P,store:()=>m});var n={};e.r(n),e.d(n,{disableTips:()=>l,dismissTip:()=>u,enableTips:()=>a,triggerGuide:()=>p});var t={};e.r(t),e.d(t,{areTipsEnabled:()=>T,getAssociatedGuide:()=>w,isTipVisible:()=>f});const s=window.wp.deprecated;var r=e.n(s);const o=window.wp.data;const c=(0,o.combineReducers)({areTipsEnabled:function(e=!0,i){switch(i.type){case"DISABLE_TIPS":return!1;case"ENABLE_TIPS":return!0}return e},dismissedTips:function(e={},i){switch(i.type){case"DISMISS_TIP":return{...e,[i.id]:!0};case"ENABLE_TIPS":return{}}return e}}),d=(0,o.combineReducers)({guides:function(e=[],i){return"TRIGGER_GUIDE"===i.type?[...e,i.tipIds]:e},preferences:c});function p(e){return{type:"TRIGGER_GUIDE",tipIds:e}}function u(e){return{type:"DISMISS_TIP",id:e}}function l(){return{type:"DISABLE_TIPS"}}function a(){return{type:"ENABLE_TIPS"}}const w=(0,o.createSelector)(((e,i)=>{for(const n of e.guides)if(n.includes(i)){const i=n.filter((i=>!Object.keys(e.preferences.dismissedTips).includes(i))),[t=null,s=null]=i;return{tipIds:n,currentTipId:t,nextTipId:s}}return null}),(e=>[e.guides,e.preferences.dismissedTips]));function f(e,i){if(!e.preferences.areTipsEnabled)return!1;if(e.preferences.dismissedTips?.hasOwnProperty(i))return!1;const n=w(e,i);return!n||n.currentTipId===i}function T(e){return e.preferences.areTipsEnabled}const b="core/nux",m=(0,o.createReduxStore)(b,{reducer:d,actions:n,selectors:t,persist:["preferences"]});(0,o.registerStore)(b,{reducer:d,actions:n,selectors:t,persist:["preferences"]});const I=window.wp.compose,S=window.wp.components,_=window.wp.i18n,x=window.wp.element,g=window.wp.primitives,h=window.ReactJSXRuntime,y=(0,h.jsx)(g.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,h.jsx)(g.Path,{d:"m13.06 12 6.47-6.47-1.06-1.06L12 10.94 5.53 4.47 4.47 5.53 10.94 12l-6.47 6.47 1.06 1.06L12 13.06l6.47 6.47 1.06-1.06L13.06 12Z"})});function E(e){e.stopPropagation()}const P=(0,I.compose)((0,o.withSelect)(((e,{tipId:i})=>{const{isTipVisible:n,getAssociatedGuide:t}=e(m),s=t(i);return{isVisible:n(i),hasNextTip:!(!s||!s.nextTipId)}})),(0,o.withDispatch)(((e,{tipId:i})=>{const{dismissTip:n,disableTips:t}=e(m);return{onDismiss(){n(i)},onDisable(){t()}}})))((function({position:e="middle right",children:i,isVisible:n,hasNextTip:t,onDismiss:s,onDisable:r}){const o=(0,x.useRef)(null),c=(0,x.useCallback)((e=>{o.current&&(o.current.contains(e.relatedTarget)||r())}),[r,o]);return n?(0,h.jsxs)(S.Popover,{className:"nux-dot-tip",position:e,focusOnMount:!0,role:"dialog","aria-label":(0,_.__)("Editor tips"),onClick:E,onFocusOutside:c,children:[(0,h.jsx)("p",{children:i}),(0,h.jsx)("p",{children:(0,h.jsx)(S.Button,{__next40pxDefaultSize:!0,variant:"link",onClick:s,children:t?(0,_.__)("See next tip"):(0,_.__)("Got it")})}),(0,h.jsx)(S.Button,{size:"small",className:"nux-dot-tip__disable",icon:y,label:(0,_.__)("Disable tips"),onClick:r})]}):null}));r()("wp.nux",{since:"5.4",hint:"wp.components.Guide can be used to show a user guide.",version:"6.2"}),(window.wp=window.wp||{}).nux=i})();