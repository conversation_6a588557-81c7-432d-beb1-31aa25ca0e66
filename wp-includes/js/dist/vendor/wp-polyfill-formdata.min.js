/*! formdata-polyfill. MIT License. Jimmy W?rting <https://jimmy.warting.se/opensource> */
!function(){var t;function e(t){var e=0;return function(){return e<t.length?{done:!1,value:t[e++]}:{done:!0}}}var n="function"==typeof Object.defineProperties?Object.defineProperty:function(t,e,n){return t==Array.prototype||t==Object.prototype||(t[e]=n.value),t};var r,o=function(t){t=["object"==typeof globalThis&&globalThis,t,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var e=0;e<t.length;++e){var n=t[e];if(n&&n.Math==Math)return n}throw Error("Cannot find global object")}(this);function i(t,e){if(e)t:{var r=o;t=t.split(".");for(var i=0;i<t.length-1;i++){var a=t[i];if(!(a in r))break t;r=r[a]}(e=e(i=r[t=t[t.length-1]]))!=i&&null!=e&&n(r,t,{configurable:!0,writable:!0,value:e})}}function a(t){return(t={next:t})[Symbol.iterator]=function(){return this},t}function u(t){var n="undefined"!=typeof Symbol&&Symbol.iterator&&t[Symbol.iterator];return n?n.call(t):{next:e(t)}}if(i("Symbol",(function(t){function e(t,e){this.A=t,n(this,"description",{configurable:!0,writable:!0,value:e})}if(t)return t;e.prototype.toString=function(){return this.A};var r="jscomp_symbol_"+(1e9*Math.random()>>>0)+"_",o=0;return function t(n){if(this instanceof t)throw new TypeError("Symbol is not a constructor");return new e(r+(n||"")+"_"+o++,n)}})),i("Symbol.iterator",(function(t){if(t)return t;t=Symbol("Symbol.iterator");for(var r="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),i=0;i<r.length;i++){var u=o[r[i]];"function"==typeof u&&"function"!=typeof u.prototype[t]&&n(u.prototype,t,{configurable:!0,writable:!0,value:function(){return a(e(this))}})}return t})),"function"==typeof Object.setPrototypeOf)r=Object.setPrototypeOf;else{var l;t:{var s={};try{s.__proto__={a:!0},l=s.a;break t}catch(t){}l=!1}r=l?function(t,e){if(t.__proto__=e,t.__proto__!==e)throw new TypeError(t+" is not extensible");return t}:null}var f=r;function c(){this.m=!1,this.j=null,this.v=void 0,this.h=1,this.u=this.C=0,this.l=null}function h(t){if(t.m)throw new TypeError("Generator is already running");t.m=!0}function p(t,e){return t.h=3,{value:e}}function y(t){this.g=new c,this.G=t}function v(t,e,n,r){try{var o=e.call(t.g.j,n);if(!(o instanceof Object))throw new TypeError("Iterator result "+o+" is not an object");if(!o.done)return t.g.m=!1,o;var i=o.value}catch(e){return t.g.j=null,t.g.s(e),g(t)}return t.g.j=null,r.call(t.g,i),g(t)}function g(t){for(;t.g.h;)try{var e=t.G(t.g);if(e)return t.g.m=!1,{value:e.value,done:!1}}catch(e){t.g.v=void 0,t.g.s(e)}if(t.g.m=!1,t.g.l){if(e=t.g.l,t.g.l=null,e.F)throw e.D;return{value:e.return,done:!0}}return{value:void 0,done:!0}}function d(t){this.next=function(e){return t.o(e)},this.throw=function(e){return t.s(e)},this.return=function(e){return function(t,e){h(t.g);var n=t.g.j;return n?v(t,"return"in n?n.return:function(t){return{value:t,done:!0}},e,t.g.return):(t.g.return(e),g(t))}(t,e)},this[Symbol.iterator]=function(){return this}}function b(t,e){return e=new d(new y(e)),f&&t.prototype&&f(e,t.prototype),e}if(c.prototype.o=function(t){this.v=t},c.prototype.s=function(t){this.l={D:t,F:!0},this.h=this.C||this.u},c.prototype.return=function(t){this.l={return:t},this.h=this.u},y.prototype.o=function(t){return h(this.g),this.g.j?v(this,this.g.j.next,t,this.g.o):(this.g.o(t),g(this))},y.prototype.s=function(t){return h(this.g),this.g.j?v(this,this.g.j.throw,t,this.g.o):(this.g.s(t),g(this))},i("Array.prototype.entries",(function(t){return t||function(){return function(t,e){t instanceof String&&(t+="");var n=0,r=!1,o={next:function(){if(!r&&n<t.length){var o=n++;return{value:e(o,t[o]),done:!1}}return r=!0,{done:!0,value:void 0}}};return o[Symbol.iterator]=function(){return o},o}(this,(function(t,e){return[t,e]}))}})),"undefined"!=typeof Blob&&("undefined"==typeof FormData||!FormData.prototype.keys)){var m=function(t,e){for(var n=0;n<t.length;n++)e(t[n])},w=function(t){return t.replace(/\r?\n|\r/g,"\r\n")},S=function(t,e,n){return e instanceof Blob?(n=void 0!==n?String(n+""):"string"==typeof e.name?e.name:"blob",e.name===n&&"[object Blob]"!==Object.prototype.toString.call(e)||(e=new File([e],n)),[String(t),e]):[String(t),String(e)]},j=function(t,e){if(t.length<e)throw new TypeError(e+" argument required, but only "+t.length+" present.")},x="object"==typeof globalThis?globalThis:"object"==typeof window?window:"object"==typeof self?self:this,_=x.FormData,F=x.XMLHttpRequest&&x.XMLHttpRequest.prototype.send,A=x.Request&&x.fetch,M=x.navigator&&x.navigator.sendBeacon,D=x.Element&&x.Element.prototype,B=x.Symbol&&Symbol.toStringTag;B&&(Blob.prototype[B]||(Blob.prototype[B]="Blob"),"File"in x&&!File.prototype[B]&&(File.prototype[B]="File"));try{new File([],"")}catch(t){x.File=function(t,e,n){return t=new Blob(t,n||{}),Object.defineProperties(t,{name:{value:e},lastModified:{value:+(n&&void 0!==n.lastModified?new Date(n.lastModified):new Date)},toString:{value:function(){return"[object File]"}}}),B&&Object.defineProperty(t,B,{value:"File"}),t}}var T=function(t){return t.replace(/\n/g,"%0A").replace(/\r/g,"%0D").replace(/"/g,"%22")},q=function(t){this.i=[];var e=this;t&&m(t.elements,(function(t){if(t.name&&!t.disabled&&"submit"!==t.type&&"button"!==t.type&&!t.matches("form fieldset[disabled] *"))if("file"===t.type){var n=t.files&&t.files.length?t.files:[new File([],"",{type:"application/octet-stream"})];m(n,(function(n){e.append(t.name,n)}))}else"select-multiple"===t.type||"select-one"===t.type?m(t.options,(function(n){!n.disabled&&n.selected&&e.append(t.name,n.value)})):"checkbox"===t.type||"radio"===t.type?t.checked&&e.append(t.name,t.value):(n="textarea"===t.type?w(t.value):t.value,e.append(t.name,n))}))};if((t=q.prototype).append=function(t,e,n){j(arguments,2),this.i.push(S(t,e,n))},t.delete=function(t){j(arguments,1);var e=[];t=String(t),m(this.i,(function(n){n[0]!==t&&e.push(n)})),this.i=e},t.entries=function t(){var e,n=this;return b(t,(function(t){if(1==t.h&&(e=0),3!=t.h)return e<n.i.length?t=p(t,n.i[e]):(t.h=0,t=void 0),t;e++,t.h=2}))},t.forEach=function(t,e){j(arguments,1);for(var n=u(this),r=n.next();!r.done;r=n.next()){var o=u(r.value);r=o.next().value,o=o.next().value,t.call(e,o,r,this)}},t.get=function(t){j(arguments,1);var e=this.i;t=String(t);for(var n=0;n<e.length;n++)if(e[n][0]===t)return e[n][1];return null},t.getAll=function(t){j(arguments,1);var e=[];return t=String(t),m(this.i,(function(n){n[0]===t&&e.push(n[1])})),e},t.has=function(t){j(arguments,1),t=String(t);for(var e=0;e<this.i.length;e++)if(this.i[e][0]===t)return!0;return!1},t.keys=function t(){var e,n,r,o,i=this;return b(t,(function(t){if(1==t.h&&(e=u(i),n=e.next()),3!=t.h)return n.done?void(t.h=0):(r=n.value,o=u(r),p(t,o.next().value));n=e.next(),t.h=2}))},t.set=function(t,e,n){j(arguments,2),t=String(t);var r=[],o=S(t,e,n),i=!0;m(this.i,(function(e){e[0]===t?i&&(i=!r.push(o)):r.push(e)})),i&&r.push(o),this.i=r},t.values=function t(){var e,n,r,o,i=this;return b(t,(function(t){if(1==t.h&&(e=u(i),n=e.next()),3!=t.h)return n.done?void(t.h=0):(r=n.value,(o=u(r)).next(),p(t,o.next().value));n=e.next(),t.h=2}))},q.prototype._asNative=function(){for(var t=new _,e=u(this),n=e.next();!n.done;n=e.next()){var r=u(n.value);n=r.next().value,r=r.next().value,t.append(n,r)}return t},q.prototype._blob=function(){var t="----formdata-polyfill-"+Math.random(),e=[],n="--"+t+'\r\nContent-Disposition: form-data; name="';return this.forEach((function(t,r){return"string"==typeof t?e.push(n+T(w(r))+'"\r\n\r\n'+w(t)+"\r\n"):e.push(n+T(w(r))+'"; filename="'+T(t.name)+'"\r\nContent-Type: '+(t.type||"application/octet-stream")+"\r\n\r\n",t,"\r\n")})),e.push("--"+t+"--"),new Blob(e,{type:"multipart/form-data; boundary="+t})},q.prototype[Symbol.iterator]=function(){return this.entries()},q.prototype.toString=function(){return"[object FormData]"},D&&!D.matches&&(D.matches=D.matchesSelector||D.mozMatchesSelector||D.msMatchesSelector||D.oMatchesSelector||D.webkitMatchesSelector||function(t){for(var e=(t=(this.document||this.ownerDocument).querySelectorAll(t)).length;0<=--e&&t.item(e)!==this;);return-1<e}),B&&(q.prototype[B]="FormData"),F){var O=x.XMLHttpRequest.prototype.setRequestHeader;x.XMLHttpRequest.prototype.setRequestHeader=function(t,e){O.call(this,t,e),"content-type"===t.toLowerCase()&&(this.B=!0)},x.XMLHttpRequest.prototype.send=function(t){t instanceof q?(t=t._blob(),this.B||this.setRequestHeader("Content-Type",t.type),F.call(this,t)):F.call(this,t)}}A&&(x.fetch=function(t,e){return e&&e.body&&e.body instanceof q&&(e.body=e.body._blob()),A.call(this,t,e)}),M&&(x.navigator.sendBeacon=function(t,e){return e instanceof q&&(e=e._asNative()),M.call(this,t,e)}),x.FormData=q}}();