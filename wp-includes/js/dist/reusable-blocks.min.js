/*! This file is auto-generated */
(()=>{"use strict";var e={d:(t,n)=>{for(var o in n)e.o(n,o)&&!e.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:n[o]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{ReusableBlocksMenuItems:()=>S,store:()=>k});var n={};e.r(n),e.d(n,{__experimentalConvertBlockToStatic:()=>i,__experimentalConvertBlocksToReusable:()=>a,__experimentalDeleteReusableBlock:()=>d,__experimentalSetEditingReusableBlock:()=>p});var o={};e.r(o),e.d(o,{__experimentalIsEditingReusableBlock:()=>_});const s=window.wp.data,r=window.wp.blockEditor,c=window.wp.blocks,l=window.wp.i18n,i=e=>({registry:t})=>{const n=t.select(r.store).getBlock(e),o=t.select("core").getEditedEntityRecord("postType","wp_block",n.attributes.ref),s=(0,c.parse)("function"==typeof o.content?o.content(o):o.content);t.dispatch(r.store).replaceBlocks(n.clientId,s)},a=(e,t,n)=>async({registry:o,dispatch:s})=>{const i="unsynced"===n?{wp_pattern_sync_status:n}:void 0,a={title:t||(0,l.__)("Untitled pattern block"),content:(0,c.serialize)(o.select(r.store).getBlocksByClientId(e)),status:"publish",meta:i},d=await o.dispatch("core").saveEntityRecord("postType","wp_block",a);if("unsynced"===n)return;const p=(0,c.createBlock)("core/block",{ref:d.id});o.dispatch(r.store).replaceBlocks(e,p),s.__experimentalSetEditingReusableBlock(p.clientId,!0)},d=e=>async({registry:t})=>{if(!t.select("core").getEditedEntityRecord("postType","wp_block",e))return;const n=t.select(r.store).getBlocks().filter((t=>(0,c.isReusableBlock)(t)&&t.attributes.ref===e)).map((e=>e.clientId));n.length&&t.dispatch(r.store).removeBlocks(n),await t.dispatch("core").deleteEntityRecord("postType","wp_block",e)};function p(e,t){return{type:"SET_EDITING_REUSABLE_BLOCK",clientId:e,isEditing:t}}const u=(0,s.combineReducers)({isEditingReusableBlock:function(e={},t){return"SET_EDITING_REUSABLE_BLOCK"===t?.type?{...e,[t.clientId]:t.isEditing}:e}});function _(e,t){return e.isEditingReusableBlock[t]}const k=(0,s.createReduxStore)("core/reusable-blocks",{actions:n,reducer:u,selectors:o});(0,s.register)(k);const b=window.wp.element,w=window.wp.components,m=window.wp.primitives,y=window.ReactJSXRuntime,g=(0,y.jsx)(m.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,y.jsx)(m.Path,{d:"M21.3 10.8l-5.6-5.6c-.7-.7-1.8-.7-2.5 0l-5.6 5.6c-.7.7-.7 1.8 0 2.5l5.6 5.6c.3.3.8.5 1.2.5s.9-.2 1.2-.5l5.6-5.6c.8-.7.8-1.9.1-2.5zm-1 1.4l-5.6 5.6c-.1.1-.3.1-.4 0l-5.6-5.6c-.1-.1-.1-.3 0-.4l5.6-5.6s.1-.1.2-.1.1 0 .2.1l5.6 5.6c.1.1.1.3 0 .4zm-16.6-.4L10 5.5l-1-1-6.3 6.3c-.7.7-.7 1.8 0 2.5L9 19.5l1.1-1.1-6.3-6.3c-.2 0-.2-.2-.1-.3z"})}),h=window.wp.notices,x=window.wp.coreData;function B({clientIds:e,rootClientId:t,onClose:n}){const[o,i]=(0,b.useState)(void 0),[a,d]=(0,b.useState)(!1),[p,u]=(0,b.useState)(""),_=(0,s.useSelect)((n=>{var o;const{canUser:s}=n(x.store),{getBlocksByClientId:l,canInsertBlockType:i,getBlockRootClientId:a}=n(r.store),d=t||(e.length>0?a(e[0]):void 0),p=null!==(o=l(e))&&void 0!==o?o:[];return!(1===p.length&&p[0]&&(0,c.isReusableBlock)(p[0])&&!!n(x.store).getEntityRecord("postType","wp_block",p[0].attributes.ref))&&i("core/block",d)&&p.every((e=>!!e&&e.isValid&&(0,c.hasBlockSupport)(e.name,"reusable",!0)))&&!!s("create",{kind:"postType",name:"wp_block"})}),[e,t]),{__experimentalConvertBlocksToReusable:m}=(0,s.useDispatch)(k),{createSuccessNotice:B,createErrorNotice:v}=(0,s.useDispatch)(h.store),C=(0,b.useCallback)((async function(t){try{await m(e,t,o),B(o?(0,l.sprintf)((0,l.__)("Unsynced pattern created: %s"),t):(0,l.sprintf)((0,l.__)("Synced pattern created: %s"),t),{type:"snackbar",id:"convert-to-reusable-block-success"})}catch(e){v(e.message,{type:"snackbar",id:"convert-to-reusable-block-error"})}}),[m,e,o,B,v]);return _?(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(w.MenuItem,{icon:g,onClick:()=>d(!0),children:(0,l.__)("Create pattern")}),a&&(0,y.jsx)(w.Modal,{title:(0,l.__)("Create pattern"),onRequestClose:()=>{d(!1),u("")},overlayClassName:"reusable-blocks-menu-items__convert-modal",children:(0,y.jsx)("form",{onSubmit:e=>{e.preventDefault(),C(p),d(!1),u(""),n()},children:(0,y.jsxs)(w.__experimentalVStack,{spacing:"5",children:[(0,y.jsx)(w.TextControl,{__next40pxDefaultSize:!0,__nextHasNoMarginBottom:!0,label:(0,l.__)("Name"),value:p,onChange:u,placeholder:(0,l.__)("My pattern")}),(0,y.jsx)(w.ToggleControl,{__nextHasNoMarginBottom:!0,label:(0,l._x)("Synced","pattern (singular)"),help:(0,l.__)("Sync this pattern across multiple locations."),checked:!o,onChange:()=>{i(o?void 0:"unsynced")}}),(0,y.jsxs)(w.__experimentalHStack,{justify:"right",children:[(0,y.jsx)(w.Button,{__next40pxDefaultSize:!0,variant:"tertiary",onClick:()=>{d(!1),u("")},children:(0,l.__)("Cancel")}),(0,y.jsx)(w.Button,{__next40pxDefaultSize:!0,variant:"primary",type:"submit",children:(0,l.__)("Create")})]})]})})})]}):null}const v=window.wp.url;const C=function({clientId:e}){const{canRemove:t,isVisible:n,managePatternsUrl:o}=(0,s.useSelect)((t=>{const{getBlock:n,canRemoveBlock:o,getBlockCount:s}=t(r.store),{canUser:l}=t(x.store),i=n(e);return{canRemove:o(e),isVisible:!!i&&(0,c.isReusableBlock)(i)&&!!l("update",{kind:"postType",name:"wp_block",id:i.attributes.ref}),innerBlockCount:s(e),managePatternsUrl:l("create",{kind:"postType",name:"wp_template"})?(0,v.addQueryArgs)("site-editor.php",{path:"/patterns"}):(0,v.addQueryArgs)("edit.php",{post_type:"wp_block"})}}),[e]),{__experimentalConvertBlockToStatic:i}=(0,s.useDispatch)(k);return n?(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(w.MenuItem,{href:o,children:(0,l.__)("Manage patterns")}),t&&(0,y.jsx)(w.MenuItem,{onClick:()=>i(e),children:(0,l.__)("Detach")})]}):null};function S({rootClientId:e}){return(0,y.jsx)(r.BlockSettingsMenuControls,{children:({onClose:t,selectedClientIds:n})=>(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(B,{clientIds:n,rootClientId:e,onClose:t}),1===n.length&&(0,y.jsx)(C,{clientId:n[0]})]})})}(window.wp=window.wp||{}).reusableBlocks=t})();