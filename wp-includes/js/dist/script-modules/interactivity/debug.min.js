var e={380:(e,t,n)=>{n.d(t,{zj:()=>pt,SD:()=>ve,V6:()=>ye,$K:()=>me,vT:()=>ht,jb:()=>qt,yT:()=>we,M_:()=>vt,hb:()=>Oe,vJ:()=>Ee,ip:()=>xe,Nf:()=>Te,Kr:()=>Pe,li:()=>b,J0:()=>m,FH:()=>Se,v4:()=>ke,mh:()=>Ne});var r,o,i,s,a=n(622),u=0,c=[],l=a.fF,f=l.__b,_=l.__r,p=l.diffed,h=l.__c,d=l.unmount,v=l.__;function y(e,t){l.__h&&l.__h(o,e,u||t),u=0;var n=o.__H||(o.__H={__:[],__h:[]});return e>=n.__.length&&n.__.push({}),n.__[e]}function m(e){return u=1,function(e,t,n){var i=y(r++,2);if(i.t=e,!i.__c&&(i.__=[n?n(t):N(void 0,t),function(e){var t=i.__N?i.__N[0]:i.__[0],n=i.t(t,e);t!==n&&(i.__N=[n,i.__[1]],i.__c.setState({}))}],i.__c=o,!o.__f)){var s=function(e,t,n){if(!i.__c.__H)return!0;var r=i.__c.__H.__.filter((function(e){return!!e.__c}));if(r.every((function(e){return!e.__N})))return!a||a.call(this,e,t,n);var o=i.__c.props!==e;return r.forEach((function(e){if(e.__N){var t=e.__[0];e.__=e.__N,e.__N=void 0,t!==e.__[0]&&(o=!0)}})),a&&a.call(this,e,t,n)||o};o.__f=!0;var a=o.shouldComponentUpdate,u=o.componentWillUpdate;o.componentWillUpdate=function(e,t,n){if(this.__e){var r=a;a=void 0,s(e,t,n),a=r}u&&u.call(this,e,t,n)},o.shouldComponentUpdate=s}return i.__N||i.__}(N,e)}function g(e,t){var n=y(r++,3);!l.__s&&C(n.__H,t)&&(n.__=e,n.u=t,o.__H.__h.push(n))}function w(e,t){var n=y(r++,4);!l.__s&&C(n.__H,t)&&(n.__=e,n.u=t,o.__h.push(n))}function b(e){return u=5,k((function(){return{current:e}}),[])}function k(e,t){var n=y(r++,7);return C(n.__H,t)&&(n.__=e(),n.__H=t,n.__h=e),n.__}function S(e,t){return u=8,k((function(){return e}),t)}function x(e){var t=o.context[e.__c],n=y(r++,9);return n.c=e,t?(null==n.__&&(n.__=!0,t.sub(o)),t.props.value):e.__}function E(){for(var e;e=c.shift();)if(e.__P&&e.__H)try{e.__H.__h.forEach(P),e.__H.__h.forEach(F),e.__H.__h=[]}catch(t){e.__H.__h=[],l.__e(t,e.__v)}}l.__b=function(e){o=null,f&&f(e)},l.__=function(e,t){e&&t.__k&&t.__k.__m&&(e.__m=t.__k.__m),v&&v(e,t)},l.__r=function(e){_&&_(e),r=0;var t=(o=e.__c).__H;t&&(i===o?(t.__h=[],o.__h=[],t.__.forEach((function(e){e.__N&&(e.__=e.__N),e.u=e.__N=void 0}))):(t.__h.forEach(P),t.__h.forEach(F),t.__h=[],r=0)),i=o},l.diffed=function(e){p&&p(e);var t=e.__c;t&&t.__H&&(t.__H.__h.length&&(1!==c.push(t)&&s===l.requestAnimationFrame||((s=l.requestAnimationFrame)||O)(E)),t.__H.__.forEach((function(e){e.u&&(e.__H=e.u),e.u=void 0}))),i=o=null},l.__c=function(e,t){t.some((function(e){try{e.__h.forEach(P),e.__h=e.__h.filter((function(e){return!e.__||F(e)}))}catch(n){t.some((function(e){e.__h&&(e.__h=[])})),t=[],l.__e(n,e.__v)}})),h&&h(e,t)},l.unmount=function(e){d&&d(e);var t,n=e.__c;n&&n.__H&&(n.__H.__.forEach((function(e){try{P(e)}catch(e){t=e}})),n.__H=void 0,t&&l.__e(t,n.__v))};var T="function"==typeof requestAnimationFrame;function O(e){var t,n=function(){clearTimeout(r),T&&cancelAnimationFrame(t),setTimeout(e)},r=setTimeout(n,100);T&&(t=requestAnimationFrame(n))}function P(e){var t=o,n=e.__c;"function"==typeof n&&(e.__c=void 0,n()),o=t}function F(e){var t=o;e.__c=e.__(),o=t}function C(e,t){return!e||e.length!==t.length||t.some((function(t,n){return t!==e[n]}))}function N(e,t){return"function"==typeof t?t(e):t}var j=Symbol.for("preact-signals");function M(){if(W>1)W--;else{for(var e,t=!1;void 0!==A;){var n=A;for(A=void 0,D++;void 0!==n;){var r=n.o;if(n.o=void 0,n.f&=-3,!(8&n.f)&&V(n))try{n.c()}catch(n){t||(e=n,t=!0)}n=r}}if(D=0,W--,t)throw e}}function $(e){if(W>0)return e();W++;try{return e()}finally{M()}}var H=void 0;var U,A=void 0,W=0,D=0,L=0;function I(e){if(void 0!==H){var t=e.n;if(void 0===t||t.t!==H)return t={i:0,S:e,p:H.s,n:void 0,t:H,e:void 0,x:void 0,r:t},void 0!==H.s&&(H.s.n=t),H.s=t,e.n=t,32&H.f&&e.S(t),t;if(-1===t.i)return t.i=0,void 0!==t.n&&(t.n.p=t.p,void 0!==t.p&&(t.p.n=t.n),t.p=H.s,t.n=void 0,H.s.n=t,H.s=t),t}}function R(e){this.v=e,this.i=0,this.n=void 0,this.t=void 0}function z(e){return new R(e)}function V(e){for(var t=e.s;void 0!==t;t=t.n)if(t.S.i!==t.i||!t.S.h()||t.S.i!==t.i)return!0;return!1}function B(e){for(var t=e.s;void 0!==t;t=t.n){var n=t.S.n;if(void 0!==n&&(t.r=n),t.S.n=t,t.i=-1,void 0===t.n){e.s=t;break}}}function J(e){for(var t=e.s,n=void 0;void 0!==t;){var r=t.p;-1===t.i?(t.S.U(t),void 0!==r&&(r.n=t.n),void 0!==t.n&&(t.n.p=r)):n=t,t.S.n=t.r,void 0!==t.r&&(t.r=void 0),t=r}e.s=n}function K(e){R.call(this,void 0),this.x=e,this.s=void 0,this.g=L-1,this.f=4}function q(e){return new K(e)}function Y(e){var t=e.u;if(e.u=void 0,"function"==typeof t){W++;var n=H;H=void 0;try{t()}catch(t){throw e.f&=-2,e.f|=8,X(e),t}finally{H=n,M()}}}function X(e){for(var t=e.s;void 0!==t;t=t.n)t.S.U(t);e.x=void 0,e.s=void 0,Y(e)}function G(e){if(H!==this)throw new Error("Out-of-order effect");J(this),H=e,this.f&=-2,8&this.f&&X(this),M()}function Q(e){this.x=e,this.u=void 0,this.s=void 0,this.o=void 0,this.f=32}function Z(e){var t=new Q(e);try{t.c()}catch(e){throw t.d(),e}return t.d.bind(t)}function ee(e,t){a.fF[e]=t.bind(null,a.fF[e]||function(){})}function te(e){U&&U(),U=e&&e.S()}function ne(e){var t=this,n=e.data,r=function(e){return k((function(){return z(e)}),[])}(n);r.value=n;var o=k((function(){for(var e=t.__v;e=e.__;)if(e.__c){e.__c.__$f|=4;break}return t.__$u.c=function(){var e,n=t.__$u.S(),r=o.value;n(),(0,a.zO)(r)||3!==(null==(e=t.base)?void 0:e.nodeType)?(t.__$f|=1,t.setState({})):t.base.data=r},q((function(){var e=r.value.value;return 0===e?0:!0===e?"":e||""}))}),[]);return o.value}function re(e,t,n,r){var o=t in e&&void 0===e.ownerSVGElement,i=z(n);return{o:function(e,t){i.value=e,r=t},d:Z((function(){var n=i.value.value;r[t]!==n&&(r[t]=n,o?e[t]=n:n?e.setAttribute(t,n):e.removeAttribute(t))}))}}R.prototype.brand=j,R.prototype.h=function(){return!0},R.prototype.S=function(e){this.t!==e&&void 0===e.e&&(e.x=this.t,void 0!==this.t&&(this.t.e=e),this.t=e)},R.prototype.U=function(e){if(void 0!==this.t){var t=e.e,n=e.x;void 0!==t&&(t.x=n,e.e=void 0),void 0!==n&&(n.e=t,e.x=void 0),e===this.t&&(this.t=n)}},R.prototype.subscribe=function(e){var t=this;return Z((function(){var n=t.value,r=H;H=void 0;try{e(n)}finally{H=r}}))},R.prototype.valueOf=function(){return this.value},R.prototype.toString=function(){return this.value+""},R.prototype.toJSON=function(){return this.value},R.prototype.peek=function(){var e=H;H=void 0;try{return this.value}finally{H=e}},Object.defineProperty(R.prototype,"value",{get:function(){var e=I(this);return void 0!==e&&(e.i=this.i),this.v},set:function(e){if(e!==this.v){if(D>100)throw new Error("Cycle detected");this.v=e,this.i++,L++,W++;try{for(var t=this.t;void 0!==t;t=t.x)t.t.N()}finally{M()}}}}),(K.prototype=new R).h=function(){if(this.f&=-3,1&this.f)return!1;if(32==(36&this.f))return!0;if(this.f&=-5,this.g===L)return!0;if(this.g=L,this.f|=1,this.i>0&&!V(this))return this.f&=-2,!0;var e=H;try{B(this),H=this;var t=this.x();(16&this.f||this.v!==t||0===this.i)&&(this.v=t,this.f&=-17,this.i++)}catch(e){this.v=e,this.f|=16,this.i++}return H=e,J(this),this.f&=-2,!0},K.prototype.S=function(e){if(void 0===this.t){this.f|=36;for(var t=this.s;void 0!==t;t=t.n)t.S.S(t)}R.prototype.S.call(this,e)},K.prototype.U=function(e){if(void 0!==this.t&&(R.prototype.U.call(this,e),void 0===this.t)){this.f&=-33;for(var t=this.s;void 0!==t;t=t.n)t.S.U(t)}},K.prototype.N=function(){if(!(2&this.f)){this.f|=6;for(var e=this.t;void 0!==e;e=e.x)e.t.N()}},Object.defineProperty(K.prototype,"value",{get:function(){if(1&this.f)throw new Error("Cycle detected");var e=I(this);if(this.h(),void 0!==e&&(e.i=this.i),16&this.f)throw this.v;return this.v}}),Q.prototype.c=function(){var e=this.S();try{if(8&this.f)return;if(void 0===this.x)return;var t=this.x();"function"==typeof t&&(this.u=t)}finally{e()}},Q.prototype.S=function(){if(1&this.f)throw new Error("Cycle detected");this.f|=1,this.f&=-9,Y(this),B(this),W++;var e=H;return H=this,G.bind(this,e)},Q.prototype.N=function(){2&this.f||(this.f|=2,this.o=A,A=this)},Q.prototype.d=function(){this.f|=8,1&this.f||X(this)},ne.displayName="_st",Object.defineProperties(R.prototype,{constructor:{configurable:!0,value:void 0},type:{configurable:!0,value:ne},props:{configurable:!0,get:function(){return{data:this}}},__b:{configurable:!0,value:1}}),ee("__b",(function(e,t){if("string"==typeof t.type){var n,r=t.props;for(var o in r)if("children"!==o){var i=r[o];i instanceof R&&(n||(t.__np=n={}),n[o]=i,r[o]=i.peek())}}e(t)})),ee("__r",(function(e,t){te();var n,r=t.__c;r&&(r.__$f&=-2,void 0===(n=r.__$u)&&(r.__$u=n=function(){var e;return Z((function(){e=this})),e.c=function(){r.__$f|=1,r.setState({})},e}())),te(n),e(t)})),ee("__e",(function(e,t,n,r){te(),e(t,n,r)})),ee("diffed",(function(e,t){var n;if(te(),"string"==typeof t.type&&(n=t.__e)){var r=t.__np,o=t.props;if(r){var i=n.U;if(i)for(var s in i){var a=i[s];void 0===a||s in r||(a.d(),i[s]=void 0)}else n.U=i={};for(var u in r){var c=i[u],l=r[u];void 0===c?(c=re(n,u,l,o),i[u]=c):c.o(l,o)}}}e(t)})),ee("unmount",(function(e,t){if("string"==typeof t.type){var n=t.__e;if(n){var r=n.U;if(r)for(var o in n.U=void 0,r){var i=r[o];i&&i.d()}}}else{var s=t.__c;if(s){var a=s.__$u;a&&(s.__$u=void 0,a.d())}}e(t)})),ee("__h",(function(e,t,n,r){(r<3||9===r)&&(t.__$f|=2),e(t,n,r)})),a.uA.prototype.shouldComponentUpdate=function(e,t){var n=this.__$u,r=n&&void 0!==n.s;for(var o in t)return!0;if(this.__f||"boolean"==typeof this.u&&!0===this.u){if(!(r||2&this.__$f||4&this.__$f))return!0;if(1&this.__$f)return!0}else{if(!(r||4&this.__$f))return!0;if(3&this.__$f)return!0}for(var i in e)if("__source"!==i&&e[i]!==this.props[i])return!0;for(var s in this.props)if(!(s in e))return!0;return!1};const oe=[],ie=()=>oe.slice(-1)[0],se=e=>{oe.push(e)},ae=()=>{oe.pop()},ue=[],ce=()=>ue.slice(-1)[0],le=e=>{ue.push(e)},fe=()=>{ue.pop()},_e=new WeakMap,pe=()=>{throw new Error("Please use `data-wp-bind` to modify the attributes of an element.")},he={get(e,t,n){const r=Reflect.get(e,t,n);return r&&"object"==typeof r?de(r):r},set:pe,deleteProperty:pe},de=e=>(_e.has(e)||_e.set(e,new Proxy(e,he)),_e.get(e)),ve=e=>ce().context[e||ie()],ye=()=>{const e=ce();const{ref:t,attributes:n}=e;return Object.freeze({ref:t.current,attributes:de(n)})},me=e=>ce().serverContext[e||ie()],ge=e=>new Promise((t=>{const n=()=>{clearTimeout(r),window.cancelAnimationFrame(o),setTimeout((()=>{e(),t()}))},r=setTimeout(n,100),o=window.requestAnimationFrame(n)})),we="function"==typeof window.scheduler?.yield?window.scheduler.yield.bind(window.scheduler):()=>new Promise((e=>{setTimeout(e,0)}));function be(e){g((()=>{let t=null,n=!1;return t=function(e,t){let n=()=>{};const r=Z((function(){return n=this.c.bind(this),this.x=e,this.c=t,e()}));return{flush:n,dispose:r}}(e,(async()=>{t&&!n&&(n=!0,await ge(t.flush),n=!1)})),t.dispose}),[])}function ke(e){const t=ce(),n=ie();let r;r="GeneratorFunction"===e?.constructor?.name?async(...r)=>{const o=e(...r);let i,s;for(;;){se(n),le(t);try{s=o.next(i)}finally{fe(),ae()}try{i=await s.value}catch(e){se(n),le(t),o.throw(e)}finally{fe(),ae()}if(s.done)break}return i}:(...r)=>{se(n),le(t);try{return e(...r)}finally{ae(),fe()}};if(e.sync){const e=r;return e.sync=!0,e}return r}function Se(e){be(ke(e))}function xe(e){g(ke(e),[])}function Ee(e,t){g(ke(e),t)}function Te(e,t){w(ke(e),t)}function Oe(e,t){return S(ke(e),t)}function Pe(e,t){return k(ke(e),t)}new Set;const Fe=e=>{0},Ce=e=>Boolean(e&&"object"==typeof e&&e.constructor===Object);function Ne(e){const t=e;return t.sync=!0,t}const je=new WeakMap,Me=new WeakMap,$e=new WeakMap,He=new Set([Object,Array]),Ue=(e,t,n)=>{if(!De(t))throw Error("This object cannot be proxified.");if(!je.has(t)){const r=new Proxy(t,n);je.set(t,r),Me.set(r,t),$e.set(r,e)}return je.get(t)},Ae=e=>je.get(e),We=e=>$e.get(e),De=e=>"object"==typeof e&&null!==e&&(!$e.has(e)&&He.has(e.constructor)),Le={};class Ie{constructor(e){this.owner=e,this.computedsByScope=new WeakMap}setValue(e){this.update({value:e})}setGetter(e){this.update({get:e})}getComputed(){const e=ce()||Le;if(this.valueSignal||this.getterSignal||this.update({}),!this.computedsByScope.has(e)){const t=()=>{const e=this.getterSignal?.value;return e?e.call(this.owner):this.valueSignal?.value};se(We(this.owner)),this.computedsByScope.set(e,q(ke(t))),ae()}return this.computedsByScope.get(e)}update({get:e,value:t}){this.valueSignal?t===this.valueSignal.peek()&&e===this.getterSignal.peek()||$((()=>{this.valueSignal.value=t,this.getterSignal.value=e})):(this.valueSignal=z(t),this.getterSignal=z(e))}}const Re=new Set(Object.getOwnPropertyNames(Symbol).map((e=>Symbol[e])).filter((e=>"symbol"==typeof e))),ze=new WeakMap,Ve=(e,t)=>ze.has(e)&&ze.get(e).has(t),Be=new WeakSet,Je=(e,t,n)=>{ze.has(e)||ze.set(e,new Map),t="number"==typeof t?`${t}`:t;const r=ze.get(e);if(!r.has(t)){const o=We(e),i=new Ie(e);if(r.set(t,i),n){const{get:t,value:r}=n;if(t)i.setGetter(t);else{const t=Be.has(e);i.setValue(De(r)?Xe(o,r,{readOnly:t}):r)}}}return r.get(t)},Ke=new WeakMap;let qe=!1;const Ye={get(e,t,n){if(qe||!e.hasOwnProperty(t)&&t in e||"symbol"==typeof t&&Re.has(t))return Reflect.get(e,t,n);const r=Object.getOwnPropertyDescriptor(e,t),o=Je(n,t,r).getComputed().value;if("function"==typeof o){const e=We(n);return(...t)=>{se(e);try{return o.call(n,...t)}finally{ae()}}}return o},set(e,t,n,r){if(Be.has(r))return!1;se(We(r));try{return Reflect.set(e,t,n,r)}finally{ae()}},defineProperty(e,t,n){if(Be.has(Ae(e)))return!1;const r=!(t in e),o=Reflect.defineProperty(e,t,n);if(o){const o=Ae(e),i=Je(o,t),{get:s,value:a}=n;if(s)i.setGetter(s);else{const e=We(o);i.setValue(De(a)?Xe(e,a):a)}if(r&&Ke.has(e)&&Ke.get(e).value++,Array.isArray(e)&&ze.get(o)?.has("length")){Je(o,"length").setValue(e.length)}}return o},deleteProperty(e,t){if(Be.has(Ae(e)))return!1;const n=Reflect.deleteProperty(e,t);if(n){Je(Ae(e),t).setValue(void 0),Ke.has(e)&&Ke.get(e).value++}return n},ownKeys:e=>(Ke.has(e)||Ke.set(e,z(0)),Ke._=Ke.get(e).value,Reflect.ownKeys(e))},Xe=(e,t,n)=>{const r=Ue(e,t,Ye);return n?.readOnly&&Be.add(r),r},Ge=(e,t,n=!0)=>{if(!Ce(e)||!Ce(t))return;let r=!1;for(const o in t){const i=!(o in e);r=r||i;const s=Object.getOwnPropertyDescriptor(t,o),a=Ae(e),u=!!a&&Ve(a,o)&&Je(a,o);if("function"==typeof s.get||"function"==typeof s.set)(n||i)&&(Object.defineProperty(e,o,{...s,configurable:!0,enumerable:!0}),s.get&&u&&u.setGetter(s.get));else if(Ce(t[o])){const r=Object.getOwnPropertyDescriptor(e,o)?.value;if(i||n&&!Ce(r)){if(e[o]={},u){const t=We(a);u.setValue(Xe(t,e[o]))}Ge(e[o],t[o],n)}else Ce(r)&&Ge(e[o],t[o],n)}else if((n||i)&&(Object.defineProperty(e,o,s),u)){const{value:e}=s,t=We(a);u.setValue(De(e)?Xe(t,e):e)}}r&&Ke.has(e)&&Ke.get(e).value++},Qe=(e,t,n=!0)=>$((()=>{return Ge((r=e,Me.get(r)||e),t,n);var r})),Ze=new WeakSet,et={get:(e,t,n)=>{const r=Reflect.get(e,t),o=We(n);if(void 0===r&&Ze.has(n)){const n={};return Reflect.set(e,t,n),tt(o,n,!1)}if("function"==typeof r){se(o);const e=ke(r);return ae(),e}return Ce(r)&&De(r)?tt(o,r,!1):r}},tt=(e,t,n=!0)=>{const r=Ue(e,t,et);return r&&n&&Ze.add(r),r},nt=new WeakMap,rt=new WeakMap,ot=new WeakSet,it=Reflect.getOwnPropertyDescriptor,st={get:(e,t)=>{const n=rt.get(e),r=e[t];return t in e?r:n[t]},set:(e,t,n)=>{const r=rt.get(e);return(t in e||!(t in r)?e:r)[t]=n,!0},ownKeys:e=>[...new Set([...Object.keys(rt.get(e)),...Object.keys(e)])],getOwnPropertyDescriptor:(e,t)=>it(e,t)||it(rt.get(e),t),has:(e,t)=>Reflect.has(e,t)||Reflect.has(rt.get(e),t)},at=(e,t={})=>{if(ot.has(e))throw Error("This object cannot be proxified.");if(rt.set(e,t),!nt.has(e)){const t=new Proxy(e,st);nt.set(e,t),ot.add(t)}return nt.get(e)},ut=new Map,ct=new Map,lt=new Map,ft=new Map,_t=new Map,pt=e=>ft.get(e||ie())||{},ht=e=>{const t=e||ie();return _t.has(t)||_t.set(t,Xe(t,{},{readOnly:!0})),_t.get(t)},dt="I acknowledge that using a private store means my plugin will inevitably break on the next store release.";function vt(e,{state:t={},...n}={},{lock:r=!1}={}){if(ut.has(e)){if(r===dt||lt.has(e)){const t=lt.get(e);if(!(r===dt||!0!==r&&r===t))throw t?Error("Cannot unlock a private store with an invalid lock code"):Error("Cannot lock a public store")}else lt.set(e,r);const o=ct.get(e);Qe(o,n),Qe(o.state,t)}else{r!==dt&&lt.set(e,r);const o={state:Xe(e,Ce(t)?t:{}),...n},i=tt(e,o);ct.set(e,o),ut.set(e,i)}return ut.get(e)}const yt=(e=document)=>{var t;const n=null!==(t=e.getElementById("wp-script-module-data-@wordpress/interactivity"))&&void 0!==t?t:e.getElementById("wp-interactivity-data");if(n?.textContent)try{return JSON.parse(n.textContent)}catch{}return{}},mt=e=>{Ce(e?.state)&&Object.entries(e.state).forEach((([e,t])=>{const n=vt(e,{},{lock:dt});Qe(n.state,t,!1),Qe(ht(e),t)})),Ce(e?.config)&&Object.entries(e.config).forEach((([e,t])=>{ft.set(e,t)}))},gt=yt();function wt(e){return null!==e.suffix}function bt(e){return null===e.suffix}mt(gt);const kt=(0,a.q6)({client:{},server:{}}),St={},xt={},Et=(e,t,{priority:n=10}={})=>{St[e]=t,xt[e]=n},Tt=({scope:e})=>(t,...n)=>{let{value:r,namespace:o}=t;if("string"!=typeof r)throw new Error("The `value` prop should be a string path");const i="!"===r[0]&&!!(r=r.slice(1));le(e);const s=((e,t)=>{if(!t)return void Fe();let n=ut.get(t);void 0===n&&(n=vt(t,{},{lock:dt}));const r={...n,context:ce().context[t]};try{return e.split(".").reduce(((e,t)=>e[t]),r)}catch(e){}})(r,o);if("function"==typeof s){if(i){Fe();const e=!s(...n);return fe(),e}return fe(),(...t)=>{le(e);const n=s(...t);return fe(),n}}const a=s;return fe(),i?!a:a},Ot=({directives:e,priorityLevels:[t,...n],element:r,originalProps:o,previousScope:i})=>{const s=b({}).current;s.evaluate=S(Tt({scope:s}),[]);const{client:u,server:c}=x(kt);s.context=u,s.serverContext=c,s.ref=i?.ref||b(null),r=(0,a.Ob)(r,{ref:s.ref}),s.attributes=r.props;const l=n.length>0?(0,a.h)(Ot,{directives:e,priorityLevels:n,element:r,originalProps:o,previousScope:s}):r,f={...o,children:l},_={directives:e,props:f,element:r,context:kt,evaluate:s.evaluate};le(s);for(const e of t){const t=St[e]?.(_);void 0!==t&&(f.children=t)}return fe(),f.children},Pt=a.fF.vnode;function Ft(e){return Ce(e)?Object.fromEntries(Object.entries(e).map((([e,t])=>[e,Ft(t)]))):Array.isArray(e)?e.map((e=>Ft(e))):e}function Ct(e){return new Proxy(e,{get(e,t,n){const r=e[t];switch(t){case"currentTarget":case"preventDefault":case"stopImmediatePropagation":case"stopPropagation":Fe()}return r instanceof Function?function(...t){return r.apply(this===n?e:this,t)}:r}})}a.fF.vnode=e=>{if(e.props.__directives){const t=e.props,n=t.__directives;n.key&&(e.key=n.key.find(bt).value),delete t.__directives;const r=(e=>{const t=Object.keys(e).reduce(((e,t)=>{if(St[t]){const n=xt[t];(e[n]=e[n]||[]).push(t)}return e}),{});return Object.entries(t).sort((([e],[t])=>parseInt(e)-parseInt(t))).map((([,e])=>e))})(n);r.length>0&&(e.props={directives:n,priorityLevels:r,originalProps:t,type:e.type,element:(0,a.h)(e.type,t),top:!0},e.type=Ot)}Pt&&Pt(e)};const Nt=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,jt=/\/\*[^]*?\*\/|  +/g,Mt=/\n+/g,$t=e=>({directives:t,evaluate:n})=>{t[`on-${e}`].filter(wt).forEach((t=>{const r=t.suffix.split("--",1)[0];xe((()=>{const o=e=>{const r=n(t);"function"==typeof r&&(r?.sync||(e=Ct(e)),r(e))},i="window"===e?window:document;return i.addEventListener(r,o),()=>i.removeEventListener(r,o)}))}))},Ht=e=>({directives:t,evaluate:n})=>{t[`on-async-${e}`].filter(wt).forEach((t=>{const r=t.suffix.split("--",1)[0];xe((()=>{const o=async e=>{await we();const r=n(t);"function"==typeof r&&r(e)},i="window"===e?window:document;return i.addEventListener(r,o,{passive:!0}),()=>i.removeEventListener(r,o)}))}))},Ut="wp",At=`data-${Ut}-ignore`,Wt=`data-${Ut}-interactive`,Dt=`data-${Ut}-`,Lt=[],It=new RegExp(`^data-${Ut}-([a-z0-9]+(?:-[a-z0-9]+)*)(?:--([a-z0-9_-]+))?$`,"i"),Rt=/^([\w_\/-]+)::(.+)$/,zt=new WeakSet;function Vt(e){const t=document.createTreeWalker(e,205);return function e(n){const{nodeType:r}=n;if(3===r)return[n.data];if(4===r){var o;const e=t.nextSibling();return n.replaceWith(new window.Text(null!==(o=n.nodeValue)&&void 0!==o?o:"")),[n.nodeValue,e]}if(8===r||7===r){const e=t.nextSibling();return n.remove(),[null,e]}const i=n,{attributes:s}=i,u=i.localName,c={},l=[],f=[];let _=!1,p=!1;for(let e=0;e<s.length;e++){const t=s[e].name,n=s[e].value;if(t[Dt.length]&&t.slice(0,Dt.length)===Dt)if(t===At)_=!0;else{var h,d;const e=Rt.exec(n),r=null!==(h=e?.[1])&&void 0!==h?h:null;let o=null!==(d=e?.[2])&&void 0!==d?d:n;try{const e=JSON.parse(o);v=e,o=Boolean(v&&"object"==typeof v&&v.constructor===Object)?e:o}catch{}if(t===Wt){p=!0;const e="string"==typeof o?o:"string"==typeof o?.namespace?o.namespace:null;Lt.push(e)}else f.push([t,r,o])}else if("ref"===t)continue;c[t]=n}var v;if(_&&!p)return[(0,a.h)(u,{...c,innerHTML:i.innerHTML,__directives:{ignore:!0}})];if(p&&zt.add(i),f.length&&(c.__directives=f.reduce(((e,[t,n,r])=>{const o=It.exec(t);if(null===o)return Fe(),e;const i=o[1]||"",s=o[2]||null;var a;return e[i]=e[i]||[],e[i].push({namespace:null!=n?n:null!==(a=Lt[Lt.length-1])&&void 0!==a?a:null,value:r,suffix:s}),e}),{})),"template"===u)c.content=[...i.content.childNodes].map((e=>Vt(e)));else{let n=t.firstChild();if(n){for(;n;){const[r,o]=e(n);r&&l.push(r),n=o||t.nextSibling()}t.parentNode()}}return p&&Lt.pop(),[(0,a.h)(u,c,l)]}(t.currentNode)}const Bt=new WeakMap,Jt=e=>{if(!e.parentElement)throw Error("The passed region should be an element with a parent.");return Bt.has(e)||Bt.set(e,((e,t)=>{const n=(t=[].concat(t))[t.length-1].nextSibling;function r(t,r){e.insertBefore(t,r||n)}return e.__k={nodeType:1,parentNode:e,firstChild:t[0],childNodes:t,insertBefore:r,appendChild:r,removeChild(t){e.removeChild(t)}}})(e.parentElement,e)),Bt.get(e)},Kt=new WeakMap,qt=e=>{if("I acknowledge that using private APIs means my theme or plugin will inevitably break in the next version of WordPress."===e)return{directivePrefix:Ut,getRegionRootFragment:Jt,initialVdom:Kt,toVdom:Vt,directive:Et,getNamespace:ie,h:a.h,cloneElement:a.Ob,render:a.XX,proxifyState:Xe,parseServerData:yt,populateServerData:mt,batch:$};throw new Error("Forbidden access.")};Et("context",(({directives:{context:e},props:{children:t},context:n})=>{const{Provider:r}=n,o=e.find(bt),{client:i,server:s}=x(n),u=o.namespace,c=b(Xe(u,{})),l=b(Xe(u,{},{readOnly:!0})),f=k((()=>{const e={client:{...i},server:{...s}};if(o){const{namespace:t,value:n}=o;Ce(n)||Fe(),Qe(c.current,Ft(n),!1),Qe(l.current,Ft(n)),e.client[t]=at(c.current,i[t]),e.server[t]=at(l.current,s[t])}return e}),[o,i,s]);return(0,a.h)(r,{value:f},t)}),{priority:5}),Et("watch",(({directives:{watch:e},evaluate:t})=>{e.forEach((e=>{Se((()=>{let n=t(e);return"function"==typeof n&&(n=n()),n}))}))})),Et("init",(({directives:{init:e},evaluate:t})=>{e.forEach((e=>{xe((()=>{let n=t(e);return"function"==typeof n&&(n=n()),n}))}))})),Et("on",(({directives:{on:e},element:t,evaluate:n})=>{const r=new Map;e.filter(wt).forEach((e=>{const t=e.suffix.split("--")[0];r.has(t)||r.set(t,new Set),r.get(t).add(e)})),r.forEach(((e,r)=>{const o=t.props[`on${r}`];t.props[`on${r}`]=t=>{e.forEach((e=>{o&&o(t);const r=n(e);"function"==typeof r&&(r?.sync||(t=Ct(t)),r(t))}))}}))})),Et("on-async",(({directives:{"on-async":e},element:t,evaluate:n})=>{const r=new Map;e.filter(wt).forEach((e=>{const t=e.suffix.split("--")[0];r.has(t)||r.set(t,new Set),r.get(t).add(e)})),r.forEach(((e,r)=>{const o=t.props[`on${r}`];t.props[`on${r}`]=t=>{o&&o(t),e.forEach((async e=>{await we();const r=n(e);"function"==typeof r&&r(t)}))}}))})),Et("on-window",$t("window")),Et("on-document",$t("document")),Et("on-async-window",Ht("window")),Et("on-async-document",Ht("document")),Et("class",(({directives:{class:e},element:t,evaluate:n})=>{e.filter(wt).forEach((e=>{const r=e.suffix;let o=n(e);"function"==typeof o&&(o=o());const i=t.props.class||"",s=new RegExp(`(^|\\s)${r}(\\s|$)`,"g");o?s.test(i)||(t.props.class=i?`${i} ${r}`:r):t.props.class=i.replace(s," ").trim(),xe((()=>{o?t.ref.current.classList.add(r):t.ref.current.classList.remove(r)}))}))})),Et("style",(({directives:{style:e},element:t,evaluate:n})=>{e.filter(wt).forEach((e=>{const r=e.suffix;let o=n(e);"function"==typeof o&&(o=o()),t.props.style=t.props.style||{},"string"==typeof t.props.style&&(t.props.style=(e=>{const t=[{}];let n,r;for(;n=Nt.exec(e.replace(jt,""));)n[4]?t.shift():n[3]?(r=n[3].replace(Mt," ").trim(),t.unshift(t[0][r]=t[0][r]||{})):t[0][n[1]]=n[2].replace(Mt," ").trim();return t[0]})(t.props.style)),o?t.props.style[r]=o:delete t.props.style[r],xe((()=>{o?t.ref.current.style[r]=o:t.ref.current.style.removeProperty(r)}))}))})),Et("bind",(({directives:{bind:e},element:t,evaluate:n})=>{e.filter(wt).forEach((e=>{const r=e.suffix;let o=n(e);"function"==typeof o&&(o=o()),t.props[r]=o,xe((()=>{const e=t.ref.current;if("style"!==r){if("width"!==r&&"height"!==r&&"href"!==r&&"list"!==r&&"form"!==r&&"tabIndex"!==r&&"download"!==r&&"rowSpan"!==r&&"colSpan"!==r&&"role"!==r&&r in e)try{return void(e[r]=null==o?"":o)}catch(e){}null==o||!1===o&&"-"!==r[4]?e.removeAttribute(r):e.setAttribute(r,o)}else"string"==typeof o&&(e.style.cssText=o)}))}))})),Et("ignore",(({element:{type:e,props:{innerHTML:t,...n}}})=>{const r=k((()=>t),[]);return(0,a.h)(e,{dangerouslySetInnerHTML:{__html:r},...n})})),Et("text",(({directives:{text:e},element:t,evaluate:n})=>{const r=e.find(bt);if(r)try{let e=n(r);"function"==typeof e&&(e=e()),t.props.children="object"==typeof e?null:e.toString()}catch(e){t.props.children=null}else t.props.children=null})),Et("run",(({directives:{run:e},evaluate:t})=>{e.forEach((e=>{let n=t(e);return"function"==typeof n&&(n=n()),n}))})),Et("each",(({directives:{each:e,"each-key":t},context:n,element:r,evaluate:o})=>{if("template"!==r.type)return;const{Provider:i}=n,s=x(n),[u]=e,{namespace:c}=u;let l=o(u);if("function"==typeof l&&(l=l()),"function"!=typeof l?.[Symbol.iterator])return;const f=wt(u)?u.suffix.replace(/^-+|-+$/g,"").toLowerCase().replace(/-([a-z])/g,(function(e,t){return t.toUpperCase()})):"item",_=[];for(const e of l){const n=at(Xe(c,{}),s.client[c]),o={client:{...s.client,[c]:n},server:{...s.server}};o.client[c][f]=e;const u={...ce(),context:o.client,serverContext:o.server},l=t?Tt({scope:u})(t[0]):e;_.push((0,a.h)(i,{value:o,key:l},r.props.content))}return _}),{priority:20}),Et("each-child",(()=>null),{priority:1}),(async()=>{const e=document.querySelectorAll(`[data-${Ut}-interactive]`);await new Promise((e=>{setTimeout(e,0)}));for(const t of e)if(!zt.has(t)){await we();const e=Jt(t),n=Vt(t);Kt.set(t,n),await we(),(0,a.Qv)(n,e)}})()},622:(e,t,n)=>{n.d(t,{FK:()=>x,Ob:()=>J,Qv:()=>B,XX:()=>V,fF:()=>o,h:()=>k,q6:()=>K,uA:()=>E,zO:()=>s});var r,o,i,s,a,u,c,l,f,_,p,h,d,v={},y=[],m=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,g=Array.isArray;function w(e,t){for(var n in t)e[n]=t[n];return e}function b(e){e&&e.parentNode&&e.parentNode.removeChild(e)}function k(e,t,n){var o,i,s,a={};for(s in t)"key"==s?o=t[s]:"ref"==s?i=t[s]:a[s]=t[s];if(arguments.length>2&&(a.children=arguments.length>3?r.call(arguments,2):n),"function"==typeof e&&null!=e.defaultProps)for(s in e.defaultProps)void 0===a[s]&&(a[s]=e.defaultProps[s]);return S(e,a,o,i,null)}function S(e,t,n,r,s){var a={type:e,props:t,key:n,ref:r,__k:null,__:null,__b:0,__e:null,__c:null,constructor:void 0,__v:null==s?++i:s,__i:-1,__u:0};return null==s&&null!=o.vnode&&o.vnode(a),a}function x(e){return e.children}function E(e,t){this.props=e,this.context=t}function T(e,t){if(null==t)return e.__?T(e.__,e.__i+1):null;for(var n;t<e.__k.length;t++)if(null!=(n=e.__k[t])&&null!=n.__e)return n.__e;return"function"==typeof e.type?T(e):null}function O(e){var t,n;if(null!=(e=e.__)&&null!=e.__c){for(e.__e=e.__c.base=null,t=0;t<e.__k.length;t++)if(null!=(n=e.__k[t])&&null!=n.__e){e.__e=e.__c.base=n.__e;break}return O(e)}}function P(e){(!e.__d&&(e.__d=!0)&&a.push(e)&&!F.__r++||u!==o.debounceRendering)&&((u=o.debounceRendering)||c)(F)}function F(){for(var e,t,n,r,i,s,u,c=1;a.length;)a.length>c&&a.sort(l),e=a.shift(),c=a.length,e.__d&&(n=void 0,i=(r=(t=e).__v).__e,s=[],u=[],t.__P&&((n=w({},r)).__v=r.__v+1,o.vnode&&o.vnode(n),A(t.__P,n,r,t.__n,t.__P.namespaceURI,32&r.__u?[i]:null,s,null==i?T(r):i,!!(32&r.__u),u),n.__v=r.__v,n.__.__k[n.__i]=n,W(s,n,u),n.__e!=i&&O(n)));F.__r=0}function C(e,t,n,r,o,i,s,a,u,c,l){var f,_,p,h,d,m,g=r&&r.__k||y,w=t.length;for(u=N(n,t,g,u,w),f=0;f<w;f++)null!=(p=n.__k[f])&&(_=-1===p.__i?v:g[p.__i]||v,p.__i=f,m=A(e,p,_,o,i,s,a,u,c,l),h=p.__e,p.ref&&_.ref!=p.ref&&(_.ref&&I(_.ref,null,p),l.push(p.ref,p.__c||h,p)),null==d&&null!=h&&(d=h),4&p.__u||_.__k===p.__k?u=j(p,u,e):"function"==typeof p.type&&void 0!==m?u=m:h&&(u=h.nextSibling),p.__u&=-7);return n.__e=d,u}function N(e,t,n,r,o){var i,s,a,u,c,l=n.length,f=l,_=0;for(e.__k=new Array(o),i=0;i<o;i++)null!=(s=t[i])&&"boolean"!=typeof s&&"function"!=typeof s?(u=i+_,(s=e.__k[i]="string"==typeof s||"number"==typeof s||"bigint"==typeof s||s.constructor==String?S(null,s,null,null,null):g(s)?S(x,{children:s},null,null,null):void 0===s.constructor&&s.__b>0?S(s.type,s.props,s.key,s.ref?s.ref:null,s.__v):s).__=e,s.__b=e.__b+1,a=null,-1!==(c=s.__i=M(s,n,u,f))&&(f--,(a=n[c])&&(a.__u|=2)),null==a||null===a.__v?(-1==c&&(o>l?_--:o<l&&_++),"function"!=typeof s.type&&(s.__u|=4)):c!=u&&(c==u-1?_--:c==u+1?_++:(c>u?_--:_++,s.__u|=4))):e.__k[i]=null;if(f)for(i=0;i<l;i++)null!=(a=n[i])&&!(2&a.__u)&&(a.__e==r&&(r=T(a)),R(a,a));return r}function j(e,t,n){var r,o;if("function"==typeof e.type){for(r=e.__k,o=0;r&&o<r.length;o++)r[o]&&(r[o].__=e,t=j(r[o],t,n));return t}e.__e!=t&&(t&&e.type&&!n.contains(t)&&(t=T(e)),n.insertBefore(e.__e,t||null),t=e.__e);do{t=t&&t.nextSibling}while(null!=t&&8==t.nodeType);return t}function M(e,t,n,r){var o,i,s=e.key,a=e.type,u=t[n];if(null===u&&null==e.key||u&&s==u.key&&a===u.type&&!(2&u.__u))return n;if(r>(null==u||2&u.__u?0:1))for(o=n-1,i=n+1;o>=0||i<t.length;){if(o>=0){if((u=t[o])&&!(2&u.__u)&&s==u.key&&a===u.type)return o;o--}if(i<t.length){if((u=t[i])&&!(2&u.__u)&&s==u.key&&a===u.type)return i;i++}}return-1}function $(e,t,n){"-"==t[0]?e.setProperty(t,null==n?"":n):e[t]=null==n?"":"number"!=typeof n||m.test(t)?n:n+"px"}function H(e,t,n,r,o){var i;e:if("style"==t)if("string"==typeof n)e.style.cssText=n;else{if("string"==typeof r&&(e.style.cssText=r=""),r)for(t in r)n&&t in n||$(e.style,t,"");if(n)for(t in n)r&&n[t]===r[t]||$(e.style,t,n[t])}else if("o"==t[0]&&"n"==t[1])i=t!=(t=t.replace(f,"$1")),t=t.toLowerCase()in e||"onFocusOut"==t||"onFocusIn"==t?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+i]=n,n?r?n.t=r.t:(n.t=_,e.addEventListener(t,i?h:p,i)):e.removeEventListener(t,i?h:p,i);else{if("http://www.w3.org/2000/svg"==o)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!=t&&"height"!=t&&"href"!=t&&"list"!=t&&"form"!=t&&"tabIndex"!=t&&"download"!=t&&"rowSpan"!=t&&"colSpan"!=t&&"role"!=t&&"popover"!=t&&t in e)try{e[t]=null==n?"":n;break e}catch(e){}"function"==typeof n||(null==n||!1===n&&"-"!=t[4]?e.removeAttribute(t):e.setAttribute(t,"popover"==t&&1==n?"":n))}}function U(e){return function(t){if(this.l){var n=this.l[t.type+e];if(null==t.u)t.u=_++;else if(t.u<n.t)return;return n(o.event?o.event(t):t)}}}function A(e,t,n,r,i,s,a,u,c,l){var f,_,p,h,d,v,y,m,k,S,T,O,P,F,N,j,M,$=t.type;if(void 0!==t.constructor)return null;128&n.__u&&(c=!!(32&n.__u),s=[u=t.__e=n.__e]),(f=o.__b)&&f(t);e:if("function"==typeof $)try{if(m=t.props,k="prototype"in $&&$.prototype.render,S=(f=$.contextType)&&r[f.__c],T=f?S?S.props.value:f.__:r,n.__c?y=(_=t.__c=n.__c).__=_.__E:(k?t.__c=_=new $(m,T):(t.__c=_=new E(m,T),_.constructor=$,_.render=z),S&&S.sub(_),_.props=m,_.state||(_.state={}),_.context=T,_.__n=r,p=_.__d=!0,_.__h=[],_._sb=[]),k&&null==_.__s&&(_.__s=_.state),k&&null!=$.getDerivedStateFromProps&&(_.__s==_.state&&(_.__s=w({},_.__s)),w(_.__s,$.getDerivedStateFromProps(m,_.__s))),h=_.props,d=_.state,_.__v=t,p)k&&null==$.getDerivedStateFromProps&&null!=_.componentWillMount&&_.componentWillMount(),k&&null!=_.componentDidMount&&_.__h.push(_.componentDidMount);else{if(k&&null==$.getDerivedStateFromProps&&m!==h&&null!=_.componentWillReceiveProps&&_.componentWillReceiveProps(m,T),!_.__e&&(null!=_.shouldComponentUpdate&&!1===_.shouldComponentUpdate(m,_.__s,T)||t.__v==n.__v)){for(t.__v!=n.__v&&(_.props=m,_.state=_.__s,_.__d=!1),t.__e=n.__e,t.__k=n.__k,t.__k.some((function(e){e&&(e.__=t)})),O=0;O<_._sb.length;O++)_.__h.push(_._sb[O]);_._sb=[],_.__h.length&&a.push(_);break e}null!=_.componentWillUpdate&&_.componentWillUpdate(m,_.__s,T),k&&null!=_.componentDidUpdate&&_.__h.push((function(){_.componentDidUpdate(h,d,v)}))}if(_.context=T,_.props=m,_.__P=e,_.__e=!1,P=o.__r,F=0,k){for(_.state=_.__s,_.__d=!1,P&&P(t),f=_.render(_.props,_.state,_.context),N=0;N<_._sb.length;N++)_.__h.push(_._sb[N]);_._sb=[]}else do{_.__d=!1,P&&P(t),f=_.render(_.props,_.state,_.context),_.state=_.__s}while(_.__d&&++F<25);_.state=_.__s,null!=_.getChildContext&&(r=w(w({},r),_.getChildContext())),k&&!p&&null!=_.getSnapshotBeforeUpdate&&(v=_.getSnapshotBeforeUpdate(h,d)),j=f,null!=f&&f.type===x&&null==f.key&&(j=D(f.props.children)),u=C(e,g(j)?j:[j],t,n,r,i,s,a,u,c,l),_.base=t.__e,t.__u&=-161,_.__h.length&&a.push(_),y&&(_.__E=_.__=null)}catch(e){if(t.__v=null,c||null!=s)if(e.then){for(t.__u|=c?160:128;u&&8==u.nodeType&&u.nextSibling;)u=u.nextSibling;s[s.indexOf(u)]=null,t.__e=u}else for(M=s.length;M--;)b(s[M]);else t.__e=n.__e,t.__k=n.__k;o.__e(e,t,n)}else null==s&&t.__v==n.__v?(t.__k=n.__k,t.__e=n.__e):u=t.__e=L(n.__e,t,n,r,i,s,a,c,l);return(f=o.diffed)&&f(t),128&t.__u?void 0:u}function W(e,t,n){for(var r=0;r<n.length;r++)I(n[r],n[++r],n[++r]);o.__c&&o.__c(t,e),e.some((function(t){try{e=t.__h,t.__h=[],e.some((function(e){e.call(t)}))}catch(e){o.__e(e,t.__v)}}))}function D(e){return"object"!=typeof e||null==e?e:g(e)?e.map(D):w({},e)}function L(e,t,n,i,s,a,u,c,l){var f,_,p,h,d,y,m,w=n.props,k=t.props,S=t.type;if("svg"==S?s="http://www.w3.org/2000/svg":"math"==S?s="http://www.w3.org/1998/Math/MathML":s||(s="http://www.w3.org/1999/xhtml"),null!=a)for(f=0;f<a.length;f++)if((d=a[f])&&"setAttribute"in d==!!S&&(S?d.localName==S:3==d.nodeType)){e=d,a[f]=null;break}if(null==e){if(null==S)return document.createTextNode(k);e=document.createElementNS(s,S,k.is&&k),c&&(o.__m&&o.__m(t,a),c=!1),a=null}if(null===S)w===k||c&&e.data===k||(e.data=k);else{if(a=a&&r.call(e.childNodes),w=n.props||v,!c&&null!=a)for(w={},f=0;f<e.attributes.length;f++)w[(d=e.attributes[f]).name]=d.value;for(f in w)if(d=w[f],"children"==f);else if("dangerouslySetInnerHTML"==f)p=d;else if(!(f in k)){if("value"==f&&"defaultValue"in k||"checked"==f&&"defaultChecked"in k)continue;H(e,f,null,d,s)}for(f in k)d=k[f],"children"==f?h=d:"dangerouslySetInnerHTML"==f?_=d:"value"==f?y=d:"checked"==f?m=d:c&&"function"!=typeof d||w[f]===d||H(e,f,d,w[f],s);if(_)c||p&&(_.__html===p.__html||_.__html===e.innerHTML)||(e.innerHTML=_.__html),t.__k=[];else if(p&&(e.innerHTML=""),C("template"===t.type?e.content:e,g(h)?h:[h],t,n,i,"foreignObject"==S?"http://www.w3.org/1999/xhtml":s,a,u,a?a[0]:n.__k&&T(n,0),c,l),null!=a)for(f=a.length;f--;)b(a[f]);c||(f="value","progress"==S&&null==y?e.removeAttribute("value"):void 0!==y&&(y!==e[f]||"progress"==S&&!y||"option"==S&&y!==w[f])&&H(e,f,y,w[f],s),f="checked",void 0!==m&&m!==e[f]&&H(e,f,m,w[f],s))}return e}function I(e,t,n){try{if("function"==typeof e){var r="function"==typeof e.__u;r&&e.__u(),r&&null==t||(e.__u=e(t))}else e.current=t}catch(e){o.__e(e,n)}}function R(e,t,n){var r,i;if(o.unmount&&o.unmount(e),(r=e.ref)&&(r.current&&r.current!==e.__e||I(r,null,t)),null!=(r=e.__c)){if(r.componentWillUnmount)try{r.componentWillUnmount()}catch(e){o.__e(e,t)}r.base=r.__P=null}if(r=e.__k)for(i=0;i<r.length;i++)r[i]&&R(r[i],t,n||"function"!=typeof e.type);n||b(e.__e),e.__c=e.__=e.__e=void 0}function z(e,t,n){return this.constructor(e,n)}function V(e,t,n){var i,s,a,u;t==document&&(t=document.documentElement),o.__&&o.__(e,t),s=(i="function"==typeof n)?null:n&&n.__k||t.__k,a=[],u=[],A(t,e=(!i&&n||t).__k=k(x,null,[e]),s||v,v,t.namespaceURI,!i&&n?[n]:s?null:t.firstChild?r.call(t.childNodes):null,a,!i&&n?n:s?s.__e:t.firstChild,i,u),W(a,e,u)}function B(e,t){V(e,t,B)}function J(e,t,n){var o,i,s,a,u=w({},e.props);for(s in e.type&&e.type.defaultProps&&(a=e.type.defaultProps),t)"key"==s?o=t[s]:"ref"==s?i=t[s]:u[s]=void 0===t[s]&&void 0!==a?a[s]:t[s];return arguments.length>2&&(u.children=arguments.length>3?r.call(arguments,2):n),S(e.type,u,o||e.key,i||e.ref,null)}function K(e){function t(e){var n,r;return this.getChildContext||(n=new Set,(r={})[t.__c]=this,this.getChildContext=function(){return r},this.componentWillUnmount=function(){n=null},this.shouldComponentUpdate=function(e){this.props.value!==e.value&&n.forEach((function(e){e.__e=!0,P(e)}))},this.sub=function(e){n.add(e);var t=e.componentWillUnmount;e.componentWillUnmount=function(){n&&n.delete(e),t&&t.call(e)}}),e.children}return t.__c="__cC"+d++,t.__=e,t.Provider=t.__l=(t.Consumer=function(e,t){return e.children(t)}).contextType=t,t}r=y.slice,o={__e:function(e,t,n,r){for(var o,i,s;t=t.__;)if((o=t.__c)&&!o.__)try{if((i=o.constructor)&&null!=i.getDerivedStateFromError&&(o.setState(i.getDerivedStateFromError(e)),s=o.__d),null!=o.componentDidCatch&&(o.componentDidCatch(e,r||{}),s=o.__d),s)return o.__E=o}catch(t){e=t}throw e}},i=0,s=function(e){return null!=e&&null==e.constructor},E.prototype.setState=function(e,t){var n;n=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=w({},this.state),"function"==typeof e&&(e=e(w({},n),this.props)),e&&w(n,e),null!=e&&this.__v&&(t&&this._sb.push(t),P(this))},E.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),P(this))},E.prototype.render=x,a=[],c="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,l=function(e,t){return e.__v.__b-t.__v.__b},F.__r=0,f=/(PointerCapture)$|Capture$/i,_=0,p=U(!1),h=U(!0),d=0}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var i=t[r]={exports:{}};return e[r](i,i.exports,n),i.exports}n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var r={};n.d(r,{zj:()=>k.zj,SD:()=>k.SD,V6:()=>k.V6,$K:()=>k.$K,vT:()=>k.vT,jb:()=>k.jb,yT:()=>k.yT,M_:()=>k.M_,hb:()=>k.hb,vJ:()=>k.vJ,ip:()=>k.ip,Nf:()=>k.Nf,Kr:()=>k.Kr,li:()=>k.li,J0:()=>k.J0,FH:()=>k.FH,v4:()=>k.v4,mh:()=>k.mh});var o,i=n(622);null!=(o="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:void 0)&&o.__PREACT_DEVTOOLS__&&o.__PREACT_DEVTOOLS__.attachPreact("10.26.4",i.fF,{Fragment:i.FK,Component:i.uA});var s={};function a(e){return e.type===i.FK?"Fragment":"function"==typeof e.type?e.type.displayName||e.type.name:"string"==typeof e.type?e.type:"#text"}var u=[],c=[];function l(){return u.length>0?u[u.length-1]:null}var f=!0;function _(e){return"function"==typeof e.type&&e.type!=i.FK}function p(e){for(var t=[e],n=e;null!=n.__o;)t.push(n.__o),n=n.__o;return t.reduce((function(e,t){e+="  in "+a(t);var n=t.__source;return n?e+=" (at "+n.fileName+":"+n.lineNumber+")":f&&console.warn("Add @babel/plugin-transform-react-jsx-source to get a more detailed component stack. Note that you should not add it to production builds of your App for bundle size reasons."),f=!1,e+"\n"}),"")}var h="function"==typeof WeakMap;function d(e){var t=[];return e.__k?(e.__k.forEach((function(e){e&&"function"==typeof e.type?t.push.apply(t,d(e)):e&&"string"==typeof e.type&&t.push(e.type)})),t):t}function v(e){return e?"function"==typeof e.type?null==e.__?null!=e.__e&&null!=e.__e.parentNode?e.__e.parentNode.localName:"":v(e.__):e.type:""}var y=i.uA.prototype.setState;function m(e){return"table"===e||"tfoot"===e||"tbody"===e||"thead"===e||"td"===e||"tr"===e||"th"===e}i.uA.prototype.setState=function(e,t){return null==this.__v&&null==this.state&&console.warn('Calling "this.setState" inside the constructor of a component is a no-op and might be a bug in your application. Instead, set "this.state = {}" directly.\n\n'+p(l())),y.call(this,e,t)};var g=/^(address|article|aside|blockquote|details|div|dl|fieldset|figcaption|figure|footer|form|h1|h2|h3|h4|h5|h6|header|hgroup|hr|main|menu|nav|ol|p|pre|search|section|table|ul)$/,w=i.uA.prototype.forceUpdate;function b(e){var t=e.props,n=a(e),r="";for(var o in t)if(t.hasOwnProperty(o)&&"children"!==o){var i=t[o];"function"==typeof i&&(i="function "+(i.displayName||i.name)+"() {}"),i=Object(i)!==i||i.toString?i+"":Object.prototype.toString.call(i),r+=" "+o+"="+JSON.stringify(i)}var s=t.children;return"<"+n+r+(s&&s.length?">..</"+n+">":" />")}i.uA.prototype.forceUpdate=function(e){return null==this.__v?console.warn('Calling "this.forceUpdate" inside the constructor of a component is a no-op and might be a bug in your application.\n\n'+p(l())):null==this.__P&&console.warn('Can\'t call "this.forceUpdate" on an unmounted component. This is a no-op, but it indicates a memory leak in your application. To fix, cancel all subscriptions and asynchronous tasks in the componentWillUnmount method.\n\n'+p(this.__v)),w.call(this,e)},i.fF.__m=function(e,t){var n=e.type,r=t.map((function(e){return e&&e.localName})).filter(Boolean);console.error('Expected a DOM node of type "'+n+'" but found "'+r.join(", ")+"\" as available DOM-node(s), this is caused by the SSR'd HTML containing different DOM-nodes compared to the hydrated one.\n\n"+p(e))},function(){!function(){var e=i.fF.__b,t=i.fF.diffed,n=i.fF.__,r=i.fF.vnode,o=i.fF.__r;i.fF.diffed=function(e){_(e)&&c.pop(),u.pop(),t&&t(e)},i.fF.__b=function(t){_(t)&&u.push(t),e&&e(t)},i.fF.__=function(e,t){c=[],n&&n(e,t)},i.fF.vnode=function(e){e.__o=c.length>0?c[c.length-1]:null,r&&r(e)},i.fF.__r=function(e){_(e)&&c.push(e),o&&o(e)}}();var e=!1,t=i.fF.__b,n=i.fF.diffed,r=i.fF.vnode,o=i.fF.__r,l=i.fF.__e,f=i.fF.__,y=i.fF.__h,w=h?{useEffect:new WeakMap,useLayoutEffect:new WeakMap,lazyPropTypes:new WeakMap}:null,k=[];i.fF.__e=function(e,t,n,r){if(t&&t.__c&&"function"==typeof e.then){var o=e;e=new Error("Missing Suspense. The throwing component was: "+a(t));for(var i=t;i;i=i.__)if(i.__c&&i.__c.__c){e=o;break}if(e instanceof Error)throw e}try{(r=r||{}).componentStack=p(t),l(e,t,n,r),"function"!=typeof e.then&&setTimeout((function(){throw e}))}catch(e){throw e}},i.fF.__=function(e,t){if(!t)throw new Error("Undefined parent passed to render(), this is the second argument.\nCheck if the element is available in the DOM/has the correct id.");var n;switch(t.nodeType){case 1:case 11:case 9:n=!0;break;default:n=!1}if(!n){var r=a(e);throw new Error("Expected a valid HTML node as a second argument to render.\tReceived "+t+" instead: render(<"+r+" />, "+t+");")}f&&f(e,t)},i.fF.__b=function(n){var r=n.type;if(e=!0,void 0===r)throw new Error("Undefined component passed to createElement()\n\nYou likely forgot to export your component or might have mixed up default and named imports"+b(n)+"\n\n"+p(n));if(null!=r&&"object"==typeof r){if(void 0!==r.__k&&void 0!==r.__e)throw new Error("Invalid type passed to createElement(): "+r+"\n\nDid you accidentally pass a JSX literal as JSX twice?\n\n  let My"+a(n)+" = "+b(r)+";\n  let vnode = <My"+a(n)+" />;\n\nThis usually happens when you export a JSX literal and not the component.\n\n"+p(n));throw new Error("Invalid type passed to createElement(): "+(Array.isArray(r)?"array":r))}if(void 0!==n.ref&&"function"!=typeof n.ref&&"object"!=typeof n.ref&&!("$$typeof"in n))throw new Error('Component\'s "ref" property should be a function, or an object created by createRef(), but got ['+typeof n.ref+"] instead\n"+b(n)+"\n\n"+p(n));if("string"==typeof n.type)for(var o in n.props)if("o"===o[0]&&"n"===o[1]&&"function"!=typeof n.props[o]&&null!=n.props[o])throw new Error("Component's \""+o+'" property should be a function, but got ['+typeof n.props[o]+"] instead\n"+b(n)+"\n\n"+p(n));if("function"==typeof n.type&&n.type.propTypes){if("Lazy"===n.type.displayName&&w&&!w.lazyPropTypes.has(n.type)){var i="PropTypes are not supported on lazy(). Use propTypes on the wrapped component itself. ";try{var u=n.type();w.lazyPropTypes.set(n.type,!0),console.warn(i+"Component wrapped in lazy() is "+a(u))}catch(e){console.warn(i+"We will log the wrapped component's name once it is loaded.")}}var c=n.props;n.type.__f&&delete(c=function(e,t){for(var n in t)e[n]=t[n];return e}({},c)).ref,function(e,t,n,r,o){Object.keys(e).forEach((function(n){var i;try{i=e[n](t,n,r,"prop",null,"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED")}catch(e){i=e}i&&!(i.message in s)&&(s[i.message]=!0,console.error("Failed prop type: "+i.message+(o&&"\n"+o()||"")))}))}(n.type.propTypes,c,0,a(n),(function(){return p(n)}))}t&&t(n)};var S,x=0;i.fF.__r=function(t){o&&o(t),e=!0;var n=t.__c;if(n===S?x++:x=1,x>=25)throw new Error("Too many re-renders. This is limited to prevent an infinite loop which may lock up your browser. The component causing this is: "+a(t));S=n},i.fF.__h=function(t,n,r){if(!t||!e)throw new Error("Hook can only be invoked from render methods.");y&&y(t,n,r)};var E=function(e,t){return{get:function(){var n="get"+e+t;k&&k.indexOf(n)<0&&(k.push(n),console.warn("getting vnode."+e+" is deprecated, "+t))},set:function(){var n="set"+e+t;k&&k.indexOf(n)<0&&(k.push(n),console.warn("setting vnode."+e+" is not allowed, "+t))}}},T={nodeName:E("nodeName","use vnode.type"),attributes:E("attributes","use vnode.props"),children:E("children","use vnode.props.children")},O=Object.create({},T);i.fF.vnode=function(e){var t=e.props;if(null!==e.type&&null!=t&&("__source"in t||"__self"in t)){var n=e.props={};for(var o in t){var i=t[o];"__source"===o?e.__source=i:"__self"===o?e.__self=i:n[o]=i}}e.__proto__=O,r&&r(e)},i.fF.diffed=function(t){var r,o=t.type,i=t.__;if(t.__k&&t.__k.forEach((function(e){if("object"==typeof e&&e&&void 0===e.type){var n=Object.keys(e).join(",");throw new Error("Objects are not valid as a child. Encountered an object with the keys {"+n+"}.\n\n"+p(t))}})),t.__c===S&&(x=0),"string"==typeof o&&(m(o)||"p"===o||"a"===o||"button"===o)){var s=v(i);if(""!==s&&m(o))"table"===o&&"td"!==s&&m(s)?console.error("Improper nesting of table. Your <table> should not have a table-node parent."+b(t)+"\n\n"+p(t)):"thead"!==o&&"tfoot"!==o&&"tbody"!==o||"table"===s?"tr"===o&&"thead"!==s&&"tfoot"!==s&&"tbody"!==s?console.error("Improper nesting of table. Your <tr> should have a <thead/tbody/tfoot> parent."+b(t)+"\n\n"+p(t)):"td"===o&&"tr"!==s?console.error("Improper nesting of table. Your <td> should have a <tr> parent."+b(t)+"\n\n"+p(t)):"th"===o&&"tr"!==s&&console.error("Improper nesting of table. Your <th> should have a <tr>."+b(t)+"\n\n"+p(t)):console.error("Improper nesting of table. Your <thead/tbody/tfoot> should have a <table> parent."+b(t)+"\n\n"+p(t));else if("p"===o){var u=d(t).filter((function(e){return g.test(e)}));u.length&&console.error("Improper nesting of paragraph. Your <p> should not have "+u.join(", ")+" as child-elements."+b(t)+"\n\n"+p(t))}else"a"!==o&&"button"!==o||-1!==d(t).indexOf(o)&&console.error("Improper nesting of interactive content. Your <"+o+"> should not have other "+("a"===o?"anchor":"button")+" tags as child-elements."+b(t)+"\n\n"+p(t))}if(e=!1,n&&n(t),null!=t.__k)for(var c=[],l=0;l<t.__k.length;l++){var f=t.__k[l];if(f&&null!=f.key){var _=f.key;if(-1!==c.indexOf(_)){console.error('Following component has two or more children with the same key attribute: "'+_+'". This may cause glitches and misbehavior in rendering process. Component: \n\n'+b(t)+"\n\n"+p(t));break}c.push(_)}}if(null!=t.__c&&null!=t.__c.__H){var h=t.__c.__H.__;if(h)for(var y=0;y<h.length;y+=1){var w=h[y];if(w.__H)for(var k=0;k<w.__H.length;k++)if((r=w.__H[k])!=r){var E=a(t);console.warn("Invalid argument passed to hook. Hooks should not be called with NaN in the dependency array. Hook index "+y+" in component "+E+" was called with NaN.")}}}}}();var k=n(380),S=r.zj,x=r.SD,E=r.V6,T=r.$K,O=r.vT,P=r.jb,F=r.yT,C=r.M_,N=r.hb,j=r.vJ,M=r.ip,$=r.Nf,H=r.Kr,U=r.li,A=r.J0,W=r.FH,D=r.v4,L=r.mh;export{S as getConfig,x as getContext,E as getElement,T as getServerContext,O as getServerState,P as privateApis,F as splitTask,C as store,N as useCallback,j as useEffect,M as useInit,$ as useLayoutEffect,H as useMemo,U as useRef,A as useState,W as useWatch,D as withScope,L as withSyncEvent};