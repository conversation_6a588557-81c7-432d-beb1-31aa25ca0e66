import*as e from"@wordpress/interactivity";var t={317:e=>{e.exports=import("@wordpress/a11y")}},o={};function i(e){var n=o[e];if(void 0!==n)return n.exports;var a=o[e]={exports:{}};return t[e](a,a.exports,i),a.exports}i.d=(e,t)=>{for(var o in t)i.o(t,o)&&!i.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var n={};i.d(n,{o:()=>D,w:()=>A});const a=(e=>{var t={};return i.d(t,e),t})({getConfig:()=>e.getConfig,privateApis:()=>e.privateApis,store:()=>e.store});new Map;var r;const{directivePrefix:s,getRegionRootFragment:d,initialVdom:l,toVdom:c,render:g,parseServerData:w,populateServerData:p,batch:u}=(0,a.privateApis)("I acknowledge that using private APIs means my theme or plugin will inevitably break in the next version of WordPress."),h=null!==(r=(0,a.getConfig)("core/router").navigationMode)&&void 0!==r?r:"regionBased",f=new Map,v=e=>{const t=new URL(e,window.location.href);return t.pathname+t.search},m=async(e,{vdom:t}={})=>{const o={body:void 0};if("regionBased"===h){const i=`data-${s}-router-region`;e.querySelectorAll(`[${i}]`).forEach((e=>{const n=e.getAttribute(i);o[n]=t?.has(e)?t.get(e):c(e)}))}const i=e.querySelector("title")?.innerText,n=w(e);return{regions:o,head:undefined,title:i,initialData:n}},y=async e=>{if("regionBased"===h){const t=`data-${s}-router-region`;u((()=>{p(e.initialData),document.querySelectorAll(`[${t}]`).forEach((o=>{const i=o.getAttribute(t),n=d(o);g(e.regions[i],n)}))}))}e.title&&(document.title=e.title)},x=e=>(window.location.assign(e),new Promise((()=>{})));window.addEventListener("popstate",(async()=>{const e=v(window.location.href),t=f.has(e)&&await f.get(e);t?(await y(t),A.url=window.location.href):window.location.reload()})),f.set(v(window.location.href),Promise.resolve(m(document,{vdom:l})));let b="",S=!1;const P={loading:"Loading page, please wait.",loaded:"Page Loaded."},{state:A,actions:D}=(0,a.store)("core/router",{state:{url:window.location.href,navigation:{hasStarted:!1,hasFinished:!1}},actions:{*navigate(e,t={}){const{clientNavigationDisabled:o}=(0,a.getConfig)();o&&(yield x(e));const i=v(e),{navigation:n}=A,{loadingAnimation:r=!0,screenReaderAnnouncement:s=!0,timeout:d=1e4}=t;b=e,D.prefetch(i,t);const l=new Promise((e=>setTimeout(e,d))),c=setTimeout((()=>{b===e&&(r&&(n.hasStarted=!0,n.hasFinished=!1),s&&C("loading"))}),400),g=yield Promise.race([f.get(i),l]);if(clearTimeout(c),b===e)if(g&&!g.initialData?.config?.["core/router"]?.clientNavigationDisabled){yield y(g),window.history[t.replace?"replaceState":"pushState"]({},"",e),A.url=e,r&&(n.hasStarted=!1,n.hasFinished=!0),s&&C("loaded");const{hash:o}=new URL(e,window.location.href);o&&document.querySelector(o)?.scrollIntoView()}else yield x(e)},prefetch(e,t={}){const{clientNavigationDisabled:o}=(0,a.getConfig)();if(o)return;const i=v(e);!t.force&&f.has(i)||f.set(i,(async(e,{html:t})=>{try{if(!t){const o=await window.fetch(e);if(200!==o.status)return!1;t=await o.text()}const o=(new window.DOMParser).parseFromString(t,"text/html");return m(o)}catch(e){return!1}})(i,{html:t.html}))}}});function C(e){if(!S){S=!0;const e=document.getElementById("wp-script-module-data-@wordpress/interactivity-router")?.textContent;if(e)try{const t=JSON.parse(e);"string"==typeof t?.i18n?.loading&&(P.loading=t.i18n.loading),"string"==typeof t?.i18n?.loaded&&(P.loaded=t.i18n.loaded)}catch{}else A.navigation.texts?.loading&&(P.loading=A.navigation.texts.loading),A.navigation.texts?.loaded&&(P.loaded=A.navigation.texts.loaded)}const t=P[e];Promise.resolve().then(i.bind(i,317)).then((({speak:e})=>e(t)),(()=>{}))}var F=n.o,L=n.w;export{F as actions,L as state};