/*! This file is auto-generated */
(()=>{"use strict";var e={n:t=>{var n=t&&t.__esModule?()=>t.default:()=>t;return e.d(n,{a:n}),n},d:(t,n)=>{for(var s in n)e.o(n,s)&&!e.o(t,s)&&Object.defineProperty(t,s,{enumerable:!0,get:n[s]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{PreferenceToggleMenuItem:()=>v,privateApis:()=>A,store:()=>j});var n={};e.r(n),e.d(n,{set:()=>f,setDefaults:()=>h,setPersistenceLayer:()=>w,toggle:()=>m});var s={};e.r(s),e.d(s,{get:()=>x});const r=window.wp.data,a=window.wp.components,o=window.wp.i18n,i=window.wp.primitives,c=window.ReactJSXRuntime,l=(0,c.jsx)(i.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,c.jsx)(i.Path,{d:"M16.7 7.1l-6.3 8.5-3.3-2.5-.9 1.2 4.5 3.4L17.9 8z"})}),d=window.wp.a11y;const p=function(e){let t;return(n,s)=>{if("SET_PERSISTENCE_LAYER"===s.type){const{persistenceLayer:e,persistedData:n}=s;return t=e,n}const r=e(n,s);return"SET_PREFERENCE_VALUE"===s.type&&t?.set(r),r}}(((e={},t)=>{if("SET_PREFERENCE_VALUE"===t.type){const{scope:n,name:s,value:r}=t;return{...e,[n]:{...e[n],[s]:r}}}return e})),u=(0,r.combineReducers)({defaults:function(e={},t){if("SET_PREFERENCE_DEFAULTS"===t.type){const{scope:n,defaults:s}=t;return{...e,[n]:{...e[n],...s}}}return e},preferences:p});function m(e,t){return function({select:n,dispatch:s}){const r=n.get(e,t);s.set(e,t,!r)}}function f(e,t,n){return{type:"SET_PREFERENCE_VALUE",scope:e,name:t,value:n}}function h(e,t){return{type:"SET_PREFERENCE_DEFAULTS",scope:e,defaults:t}}async function w(e){const t=await e.get();return{type:"SET_PERSISTENCE_LAYER",persistenceLayer:e,persistedData:t}}const g=window.wp.deprecated;var _=e.n(g);const x=(b=(e,t,n)=>{const s=e.preferences[t]?.[n];return void 0!==s?s:e.defaults[t]?.[n]},(e,t,n)=>["allowRightClickOverrides","distractionFree","editorMode","fixedToolbar","focusMode","hiddenBlockTypes","inactivePanels","keepCaretInsideBlock","mostUsedBlocks","openPanels","showBlockBreadcrumbs","showIconLabels","showListViewByDefault","isPublishSidebarEnabled","isComplementaryAreaVisible","pinnedItems"].includes(n)&&["core/edit-post","core/edit-site"].includes(t)?(_()(`wp.data.select( 'core/preferences' ).get( '${t}', '${n}' )`,{since:"6.5",alternative:`wp.data.select( 'core/preferences' ).get( 'core', '${n}' )`}),b(e,"core",n)):b(e,t,n));var b;const j=(0,r.createReduxStore)("core/preferences",{reducer:u,actions:n,selectors:s});function v({scope:e,name:t,label:n,info:s,messageActivated:i,messageDeactivated:p,shortcut:u,handleToggling:m=!0,onToggle:f=()=>null,disabled:h=!1}){const w=(0,r.useSelect)((n=>!!n(j).get(e,t)),[e,t]),{toggle:g}=(0,r.useDispatch)(j);return(0,c.jsx)(a.MenuItem,{icon:w&&l,isSelected:w,onClick:()=>{f(),m&&g(e,t),(()=>{if(w){const e=p||(0,o.sprintf)((0,o.__)("Preference deactivated - %s"),n);(0,d.speak)(e)}else{const e=i||(0,o.sprintf)((0,o.__)("Preference activated - %s"),n);(0,d.speak)(e)}})()},role:"menuitemcheckbox",info:s,shortcut:u,disabled:h,children:n})}(0,r.register)(j);const E=function({help:e,label:t,isChecked:n,onChange:s,children:r}){return(0,c.jsxs)("div",{className:"preference-base-option",children:[(0,c.jsx)(a.ToggleControl,{__nextHasNoMarginBottom:!0,help:e,label:t,checked:n,onChange:s}),r]})};const S=function(e){const{scope:t,featureName:n,onToggle:s=()=>{},...a}=e,o=(0,r.useSelect)((e=>!!e(j).get(t,n)),[t,n]),{toggle:i}=(0,r.useDispatch)(j);return(0,c.jsx)(E,{onChange:()=>{s(),i(t,n)},isChecked:o,...a})};const T=({description:e,title:t,children:n})=>(0,c.jsxs)("fieldset",{className:"preferences-modal__section",children:[(0,c.jsxs)("legend",{className:"preferences-modal__section-legend",children:[(0,c.jsx)("h2",{className:"preferences-modal__section-title",children:t}),e&&(0,c.jsx)("p",{className:"preferences-modal__section-description",children:e})]}),(0,c.jsx)("div",{className:"preferences-modal__section-content",children:n})]}),P=window.wp.compose,y=window.wp.element;const C=(0,y.forwardRef)((function({icon:e,size:t=24,...n},s){return(0,y.cloneElement)(e,{width:t,height:t,...n,ref:s})})),N=(0,c.jsx)(i.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,c.jsx)(i.Path,{d:"M14.6 7l-1.2-1L8 12l5.4 6 1.2-1-4.6-5z"})}),M=(0,c.jsx)(i.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,c.jsx)(i.Path,{d:"M10.6 6L9.4 7l4.6 5-4.6 5 1.2 1 5.4-6z"})}),R=window.wp.privateApis,{lock:k,unlock:B}=(0,R.__dangerousOptInToUnstableAPIsOnlyForCoreModules)("I acknowledge private features are not for use in themes or plugins and doing so will break in the next version of WordPress.","@wordpress/preferences"),{Tabs:L}=B(a.privateApis),I="preferences-menu";const A={};k(A,{PreferenceBaseOption:E,PreferenceToggleControl:S,PreferencesModal:function({closeModal:e,children:t}){return(0,c.jsx)(a.Modal,{className:"preferences-modal",title:(0,o.__)("Preferences"),onRequestClose:e,children:t})},PreferencesModalSection:T,PreferencesModalTabs:function({sections:e}){const t=(0,P.useViewportMatch)("medium"),[n,s]=(0,y.useState)(I),{tabs:r,sectionsContentMap:i}=(0,y.useMemo)((()=>{let t={tabs:[],sectionsContentMap:{}};return e.length&&(t=e.reduce(((e,{name:t,tabLabel:n,content:s})=>(e.tabs.push({name:t,title:n}),e.sectionsContentMap[t]=s,e)),{tabs:[],sectionsContentMap:{}})),t}),[e]);let l;return l=t?(0,c.jsx)("div",{className:"preferences__tabs",children:(0,c.jsxs)(L,{defaultTabId:n!==I?n:void 0,onSelect:s,orientation:"vertical",children:[(0,c.jsx)(L.TabList,{className:"preferences__tabs-tablist",children:r.map((e=>(0,c.jsx)(L.Tab,{tabId:e.name,className:"preferences__tabs-tab",children:e.title},e.name)))}),r.map((e=>(0,c.jsx)(L.TabPanel,{tabId:e.name,className:"preferences__tabs-tabpanel",focusable:!1,children:i[e.name]||null},e.name)))]})}):(0,c.jsxs)(a.Navigator,{initialPath:"/",className:"preferences__provider",children:[(0,c.jsx)(a.Navigator.Screen,{path:"/",children:(0,c.jsx)(a.Card,{isBorderless:!0,size:"small",children:(0,c.jsx)(a.CardBody,{children:(0,c.jsx)(a.__experimentalItemGroup,{children:r.map((e=>(0,c.jsx)(a.Navigator.Button,{path:`/${e.name}`,as:a.__experimentalItem,isAction:!0,children:(0,c.jsxs)(a.__experimentalHStack,{justify:"space-between",children:[(0,c.jsx)(a.FlexItem,{children:(0,c.jsx)(a.__experimentalTruncate,{children:e.title})}),(0,c.jsx)(a.FlexItem,{children:(0,c.jsx)(C,{icon:(0,o.isRTL)()?N:M})})]})},e.name)))})})})}),e.length&&e.map((e=>(0,c.jsx)(a.Navigator.Screen,{path:`/${e.name}`,children:(0,c.jsxs)(a.Card,{isBorderless:!0,size:"large",children:[(0,c.jsxs)(a.CardHeader,{isBorderless:!1,justify:"left",size:"small",gap:"6",children:[(0,c.jsx)(a.Navigator.BackButton,{icon:(0,o.isRTL)()?M:N,label:(0,o.__)("Back")}),(0,c.jsx)(a.__experimentalText,{size:"16",children:e.tabLabel})]}),(0,c.jsx)(a.CardBody,{children:e.content})]})},`${e.name}-menu`)))]}),l}}),(window.wp=window.wp||{}).preferences=t})();